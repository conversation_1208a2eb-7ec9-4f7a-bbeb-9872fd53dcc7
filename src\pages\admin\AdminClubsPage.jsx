import React, { useState, useEffect, lazy } from "react";
import { Box, Button, Container } from "@mui/material";

import { Client } from "../../api/client";

import { Link } from "react-router-dom";
import DynamicTable from "../../components/common/DynamicTable";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { ArrowBack } from "@mui/icons-material";
import BackButton from "../../components/common/BackButton";

import ClubSearchForm from "../../components/common/ClubSearchForm";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const AdminClubsPage = () => {
  // States for form inputs
  const [search, setSearch] = useState({
    clubName: "",
    country: "",
    state: "",
    district: "",
    city: "",
  });

  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [clubs, setClubs] = useState([]);
  const toast = UseToast();
  const { user } = UseGlobalContext();

  // Define fetchClubs function without useCallback to avoid dependency issues
  const fetchClubs = async () => {
    setLoading(true);

    try {
      const response = await Client.get("/admin/users/club", {
        params: { ...search, page, limit },
      });
      if (response.status === 204) {
        setClubs([]);
        toast.info("No clubs found");
        return;
      }
      const { clubs, currentPage: cp, totalPages: tp } = response.data.data;
      setTotalPages(tp || 1);
      setPage(cp || 1);
      setClubs(clubs);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching clubs:", error);
        toast.error("Failed to fetch clubs. Please try again.");
      }
      setClubs([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = (newpage) => {
    setPage(newpage);
    fetchClubs(newpage);
  };
  const handleReset = () => {
    setSearch({ clubName: "", country: "", state: "", district: "", city: "" });
    setPage(1);
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchClubs(newPage);
  };
  const fetchClubReport = async (type) => {
    console.log("type", type)
    // return;
    // setLoading(true);
    try {

      const response = await Client.get("/admin/report/club-report", {
        params: { type: type, ...search },
        responseType: 'blob',
      });

      console.log("data", response.data)
      const url = window.URL.createObjectURL(new Blob([response.data]));
      console.log("url", url)
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'club_report.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2, pb: 8 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <BackButton />
        {user?.role === "admin" && (
          <Box>
            <Button
              size="small"
              // disabled={!clubs.length}
              variant="contained"
              disabled={clubs?.length === 0}
              onClick={() => fetchClubReport('excel')}
            >
              {" "}
              Download Report
            </Button>
          </Box>
        )}
      </Box>
      {/* Search Form */}
      <ClubSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        loading={loading}
        handleReset={handleReset}
      />

      {/* Clubs Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          { id: "clubName", label: "Club Name" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
          { id: "city", label: "City" },
        ]}
        data={clubs}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/dashboard/clubs/"
        idField="clubId"

        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default AdminClubsPage;