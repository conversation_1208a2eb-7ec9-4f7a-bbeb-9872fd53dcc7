// utils.js or utils.ts

/**
 * Formats a 10-digit phone number into '##### #####' format
 * @param {string | number} phoneNumber - The phone number to format
 * @returns {string} - Formatted phone number
 */
export function formatPhoneNumber(phoneNumber) {
  const digits = phoneNumber.toString().replace(/\D/g, '');
  
  if (digits.length !== 10) {
    console.warn('Phone number is not 10 digits:', phoneNumber);
    return phoneNumber;
  }

  return `${digits.slice(0, 5)} ${digits.slice(5)}`;
}
