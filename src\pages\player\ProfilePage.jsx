import React, { useState, useEffect } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  Paper,
  Skeleton,
  Stack,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  CircularProgress,
  Tooltip,
  IconButton,
} from "@mui/material";
import { Person, ExitToApp, Add, Cancel } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import { DetailTable } from "../../components/common/DetailTable";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import BackButton from "../../components/common/BackButton";
import { formatDateToDMY } from "../../utils/formatters";

/**
 * Format player data for display in the detail table
 * @param {Object} player - Player data from API
 * @returns {Object} Formatted player data with title and details
 */
function formatPlayerData(player, handleLeaveClub, handleJoinClubClick) {
  if (!player) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: "Full Name",
      value: player.User?.name || "-",
    },

    {
      label: "Email",
      value: player.User?.email || "-",
    },
    { label: "Date of Birth", value: formatDateToDMY(player.dob) || "-" },
    {
      label: "Phone Number",
      value: player.User?.phoneNumber || "-",
    },
    {
      label: "FIDE ID",
      value: player.fideId || "-",
    },
    {
      label: "AICF ID",
      value: player.aicfId || "-",
    },
    {
      label: "State ID",
      value: player.stateId || "-",
    },
    {
      label: "District ID",
      value: player.districtId || "-",
    },
    {
      label: "FIDE Rating",
      value: player.fideRating || "-",
    },
    {
      label: "Club",
      value: player?.club || "-",
      hasButton: true,
      buttonText: player?.clubId ? "Leave Club" : "Join Club",
      buttonIcon: player?.clubId ? <ExitToApp /> : <Add />,
      buttonColor: player?.clubId ? "error" : "primary",
      buttonVariant: "contained",
      buttonSize: "small",
      onButtonClick: player?.clubId
        ? () => handleLeaveClub(player?.clubId)
        : () => handleJoinClubClick(),
    },
    {
      label: "Country",
      value: player.country || "-",
    },
    {
      label: "State",
      value: player.state || "-",
    },
    {
      label: "District",
      value: player.district || "-",
    },
    {
      label: "City",
      value: player.city || "-",
    },
    {
      label: "Pincode",
      value: player.pincode || "-",
    },
    {
      label: "Address",
      value: player.address || "-",
    },
  ];

  // Add parent/guardian and emergency contact if available
  if (player.parentGuardianName) {
    details.push({
      label: "Parent/Guardian Name",
      value: player.parentGuardianName,
    });
  }

  if (player.emergencyContact) {
    details.push({
      label: "Emergency Contact",
      value: player.emergencyContact,
    });
  }

  return {
    title: player.playerName || "",
    details: details,
  };
}

const PlayerProfilePage = () => {
  const [PlayerInfo, setPlayerInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [leaveClubDialogOpen, setLeaveClubDialogOpen] = useState(false);

  const [leaveLoading, setLeaveLoading] = useState(false);

  const toast = UseToast();
  const Navigate = useNavigate();

  // Handle opening the join club dialog
  const handleJoinClubClick = () => {
    Navigate("/clubs");
  };

  // Handle leaving a club
  const handleLeaveClub = () => {
    setLeaveClubDialogOpen(true);
  };

  // Confirm leaving club
  const confirmLeaveClub = async () => {
    setLeaveLoading(true);
    try {
      const response = await Client.post("/user/leave-club", {
        clubId: PlayerInfo.clubId,
      });

      if (response.data.success) {
        toast.success("You have left the club successfully");
        setLeaveClubDialogOpen(false);

        // Update player info to reflect the change
        setPlayerInfo((prev) => ({
          ...prev,
          club: null,
          clubId: null,
        }));
      } else {
        toast.error(response.data.message || "Failed to leave club");
      }
    } catch (error) {
      console.error("Error leaving club:", error);
      toast.error("An error occurred while leaving the club");
    } finally {
      setLeaveLoading(false);
    }
  };
  const handleRemoveProfile = async () => {
    try {
      const response = await Client.delete(
        "/player/profile/remove-profile-image"
      );
      if (response.data.success) {
        toast.success("Profile image removed successfully");
        setPlayerInfo((prev) => ({
          ...prev,
          profileUrl: null,
        }));
      } else {
        toast.error(response.data.message || "Failed to remove profile image");
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      toast.error("Failed to remove profile image. Please try again later.");
    }
  };

  useEffect(() => {
    const fetchPlayerInfo = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await Client.get("/player/profile", {
          params: { include: true },
        });
        if (response.status === 204) {
          Navigate("edit?edit=0");
          return;
        }

        if (!response.data.success) {
          toast.error(response.data?.error);
          setError(response.data?.error);
          return;
        }
        setPlayerInfo(response.data.data);
        setLoading(false);
      } catch (error) {
        if (error.response?.status === 401) {
          Navigate("/login");
          return;
        }
        if (error.response?.status === 404) {
          toast.error("You haven't created a profile yet.");
          Navigate("edit?edit=0");
          return;
        }
        console.error("Error fetching player info:", error);
        const errorMessage =
          error.response?.data?.error || "An unknown error occurred";
        setError(errorMessage);
        setLoading(false);
        toast.error(
          `Failed to fetch player information: ${errorMessage}. Please try again later.`
        );
      }
    };

    fetchPlayerInfo();
  }, []);

  // Format player data for display in the detail table
  const formattedPlayerData = formatPlayerData(
    PlayerInfo,
    handleLeaveClub,
    handleJoinClubClick
  );

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Paper
        elevation={1}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {error ? (
          <Typography
            variant="h6"
            color="error"
            sx={{ p: 2, textAlign: "center" }}
          >
            Something went Wrong.
          </Typography>
        ) : (
          <>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                px: "10vw",
                py: 2,
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Stack>
                  {!loading ? (
                    <>
                      <Typography variant="h5" component="h5" fontWeight="500">
                        My Profile
                      </Typography>
                      <Typography
                        variant="h6"
                        component="h6"
                        fontWeight="500"
                        fontSize={"14px"}
                      >
                        CBID: {PlayerInfo?.User.cbid}
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Skeleton variant="text" width={200} height={32} />
                      <Skeleton variant="text" width={150} height={24} />
                    </>
                  )}
                </Stack>
              </Box>
              {loading ? (
                <Skeleton variant="circular" width={100} height={100} />
              ) : (
                <Box sx={{ position: "relative", display: "inline-block" }}>
                  <Avatar
                    src={PlayerInfo?.profileUrl}
                    sx={{
                      width: 100,
                      height: 100,
                      bgcolor: "#f5f5f5",
                      color: "#000",
                    }}
                  >
                    {!PlayerInfo?.profileUrl && <Person sx={{ fontSize: 60 }} />}
                  </Avatar>

                  {PlayerInfo?.profileUrl && (
                    <Tooltip title="Remove profile image" placement="top">
                      <IconButton
                        size="small"
                        onClick={handleRemoveProfile} // define this function in your component
                        sx={{
                          position: "absolute",
                          top: 4,
                          right: 4,
                          backgroundColor: "#fff",
                          color: "red",
                          p: "2px",
                          "&:hover": {
                            backgroundColor: "#eee",
                          },
                        }}
                      >
                        <Cancel fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              )}
            </Box>

            {/* Player Information */}
            <Box>
              {loading ? (
                // Loading skeleton
                Array(6)
                  .fill(0)
                  .map((_, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Skeleton variant="rectangular" height={40} />
                    </Box>
                  ))
              ) : error ? (
                // Error message
                <Typography color="error" variant="h6" align="center">
                  {error}
                </Typography>
              ) : (
                // Player details table
                <DetailTable details={formattedPlayerData.details} />
              )}
            </Box>
            <Box sx={{ p: 2, textAlign: "center" }}>
              {PlayerInfo && (
                <Button
                  variant="contained"
                  sx={{ bgcolor: "hsla(242, 56%, 36%, 1) ", fontSize: "16px" }}
                  onClick={() => Navigate("edit?edit=1")}
                >
                  Edit Profile
                </Button>
              )}
            </Box>
          </>
        )}
      </Paper>

      {/* Leave Club Confirmation Dialog */}
      <Dialog
        open={leaveClubDialogOpen}
        onClose={() => setLeaveClubDialogOpen(false)}
      >
        <DialogTitle>Leave Club</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ color: "black" }}>
            Are you sure you want to leave {PlayerInfo?.club}? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setLeaveClubDialogOpen(false)}
            sx={{ fontSize: "16px" }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmLeaveClub}
            sx={{
              bgcolor: "red",
              color: "white",
              "&:hover": { bgcolor: "darkred" },
              fontSize: "16px",
            }}
            color="error"
            disabled={leaveLoading}
            startIcon={leaveLoading && <CircularProgress size={20} />}
          >
            {leaveLoading ? "Leaving..." : "Leave Club"}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PlayerProfilePage;
