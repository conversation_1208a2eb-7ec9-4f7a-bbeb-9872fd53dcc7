import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  Chip,
  Divider,
  CircularProgress,
  Alert,
} from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import BackButton from "../../components/common/BackButton";
import TemplateSelector from "../../components/admin/TemplateSelector";
import MessagePreview from "../../components/admin/MessagePreview";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const WhatsAppComposer = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const toast = UseToast();

  const { selectedUsers = [], selectedType = "", mode = "whatsapp" } = location.state || {};

  const [whatsappData, setWhatsappData] = useState({
    content: "",
    templateId: "",
    useTemplate: false,
  });

  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (selectedUsers.length === 0) {
      toast.error("No recipients selected");
      navigate("/dashboard/whatsapp");
      return;
    }
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/admin/whatsapp-templates");
      if (response.data.success) {
        setTemplates(response.data.data || []);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
      toast.error("Failed to load WhatsApp templates");
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template) => {
    if (template) {
      setWhatsappData(prev => ({
        ...prev,
        content: template.content || "",
        templateId: template.id,
        useTemplate: true,
      }));
    } else {
      setWhatsappData(prev => ({
        ...prev,
        content: "",
        templateId: "",
        useTemplate: false,
      }));
    }
  };

  const handleContentChange = (value) => {
    setWhatsappData(prev => ({
      ...prev,
      content: value,
      useTemplate: false, // Disable template when manually editing
    }));
  };

  const validateWhatsApp = () => {
    if (!whatsappData.content.trim()) {
      toast.error("WhatsApp message content is required");
      return false;
    }
    if (whatsappData.content.length > 4096) {
      toast.error("WhatsApp message is too long (maximum 4096 characters)");
      return false;
    }
    return true;
  };

  const handleSendWhatsApp = async () => {
    if (!validateWhatsApp()) return;

    setSending(true);
    try {
      const payload = {
        recipients: selectedUsers.map(user => ({
          id: user.id,
          phone: user.phone,
          name: user.name,
          type: user.type,
        })),
        content: whatsappData.content,
        templateId: whatsappData.useTemplate ? whatsappData.templateId : null,
        recipientType: selectedType,
      };

      const response = await Client.post("/admin/send-whatsapp", payload);

      if (response.data.success) {
        toast.success(`WhatsApp message sent successfully to ${selectedUsers.length} recipients`);
        navigate("/dashboard/whatsapp");
      } else {
        toast.error(response.data.message || "Failed to send WhatsApp message");
      }
    } catch (error) {
      console.error("Error sending WhatsApp:", error);
      toast.error("Failed to send WhatsApp message. Please try again.");
    } finally {
      setSending(false);
    }
  };

  const getRecipientSummary = () => {
    const typeLabel = selectedType.charAt(0).toUpperCase() + selectedType.slice(1);
    return `${selectedUsers.length} ${typeLabel}${selectedUsers.length > 1 ? 's' : ''}`;
  };

  const getCharacterCount = () => {
    return whatsappData.content.length;
  };

  const getRecipientsWithPhone = () => {
    return selectedUsers.filter(user => user.phone);
  };

  const getRecipientsWithoutPhone = () => {
    return selectedUsers.filter(user => !user.phone);
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton />

      <Box sx={{ mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Compose WhatsApp Message
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Send WhatsApp message to selected recipients
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Main Composition Area */}
        <Grid item xs={12} md={8}>
          {/* Recipients Summary */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recipients ({getRecipientSummary()})
            </Typography>

            {/* Recipients with phone numbers */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Recipients with phone numbers ({getRecipientsWithPhone().length}):
              </Typography>
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                {getRecipientsWithPhone().slice(0, 10).map((user, index) => (
                  <Chip
                    key={index}
                    label={`${user.name} (${user.phone})`}
                    variant="outlined"
                    size="small"
                    color="success"
                  />
                ))}
                {getRecipientsWithPhone().length > 10 && (
                  <Chip
                    label={`+${getRecipientsWithPhone().length - 10} more`}
                    variant="outlined"
                    size="small"
                    color="primary"
                  />
                )}
              </Box>
            </Box>

            {/* Recipients without phone numbers */}
            {getRecipientsWithoutPhone().length > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  {getRecipientsWithoutPhone().length} recipient(s) don't have phone numbers and will be skipped:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                  {getRecipientsWithoutPhone().slice(0, 5).map((user, index) => (
                    <Chip
                      key={index}
                      label={user.name}
                      variant="outlined"
                      size="small"
                      color="warning"
                    />
                  ))}
                  {getRecipientsWithoutPhone().length > 5 && (
                    <Chip
                      label={`+${getRecipientsWithoutPhone().length - 5} more`}
                      variant="outlined"
                      size="small"
                      color="warning"
                    />
                  )}
                </Box>
              </Alert>
            )}
          </Paper>

          {/* Template Selection */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              WhatsApp Template (Required)
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              For WhatsApp, you must select from predefined templates only.
            </Typography>
            <TemplateSelector
              templates={templates}
              selectedTemplate={whatsappData.templateId}
              onTemplateSelect={handleTemplateSelect}
              loading={loading}
              type="whatsapp"
            />
          </Paper>

          {/* WhatsApp Content */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              WhatsApp Message Content
            </Typography>

            <TextField
              label="WhatsApp Message"
              fullWidth
              multiline
              rows={8}
              value={whatsappData.content}
              onChange={(e) => handleContentChange(e.target.value)}
              placeholder="Select a template above or enter your WhatsApp message content..."
              helperText={`${getCharacterCount()}/4096 characters`}
              error={getCharacterCount() > 4096}
              disabled={!whatsappData.useTemplate} // Only allow editing if not using template
              sx={{ mb: 2 }}
            />

            {getCharacterCount() > 4096 && (
              <Alert severity="error" sx={{ mt: 1 }}>
                Message is too long. Please reduce the content to 4096 characters or less.
              </Alert>
            )}

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>WhatsApp Business API Requirements:</strong>
                <br />
                • Messages must use approved templates
                <br />
                • Templates must be pre-approved by WhatsApp
                <br />
                • Custom messages are only allowed for existing conversations
              </Typography>
            </Alert>
          </Paper>

          {/* Action Buttons */}
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
              <Button
                variant="outlined"
                onClick={() => setShowPreview(true)}
                disabled={!whatsappData.content}
              >
                Preview
              </Button>
              <Button
                variant="contained"
                color="success"
                onClick={handleSendWhatsApp}
                disabled={sending || !whatsappData.content || getRecipientsWithPhone().length === 0}
                startIcon={sending && <CircularProgress size={20} />}
              >
                {sending ? "Sending..." : `Send WhatsApp to ${getRecipientsWithPhone().length} recipients`}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, position: "sticky", top: 20 }}>
            <Typography variant="h6" gutterBottom>
              WhatsApp Summary
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Total Recipients
              </Typography>
              <Typography variant="body1">
                {selectedUsers.length}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Valid Phone Numbers
              </Typography>
              <Typography variant="body1" color={getRecipientsWithPhone().length === 0 ? "error" : "inherit"}>
                {getRecipientsWithPhone().length}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Template
              </Typography>
              <Typography variant="body1">
                {whatsappData.useTemplate ? "Using template" : "No template selected"}
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Character Count
              </Typography>
              <Typography variant="body1" color={getCharacterCount() > 4096 ? "error" : "inherit"}>
                {getCharacterCount()}/4096
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Message Type
              </Typography>
              <Typography variant="body1">
                Template Message
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Preview Modal */}
      <MessagePreview
        open={showPreview}
        onClose={() => setShowPreview(false)}
        content={whatsappData.content}
        type="whatsapp"
        recipients={getRecipientsWithPhone()}
      />
    </Container>
  );
};

export default WhatsAppComposer;