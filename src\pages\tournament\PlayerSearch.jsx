import React, { useEffect, useState } from "react";
import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  CircularProgress,
  Typography,
  Box,
} from "@mui/material";
import { RestartAlt, Search as SearchIcon } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

/**
 * Component for searching players for bulk registration
 * @param {Object} props - Component props
 * @param {Function} props.onSearch - Function to handle search results
 * @param {Object} props.tournamentDetails - Tournament details including age and gender categories
 * @param {boolean} props.loading - Loading state
 * @param {Function} props.setLoading - Function to set loading state
 */
const PlayerSearch = ({
  search,
  setSearch,
  handleSearch,
  handleReset,
  tournamentDetails,
  loading,
}) => {
  // const [branches, setBranches] = useState([]);

  const [ageCategories, setAgeCategories] = useState([]);
  // Fetch branches on component mount
  // useEffect(() => {
  //   const fetchBranches = async () => {
  //     try {
  //       const response = await Client.get("/club/branches");
  //       if (response.data.success) {
  //         setBranches(response.data.data || []);
  //       }
  //     } catch (error) {
  //       console.error("Error fetching branches:", error);
  //     }
  //   };

  //   fetchBranches();
  // }, []);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setSearch((prev) => ({ ...prev, [field]: value }));
  };

  // Handle key press in input fields (for Enter key)
  const handleKeyPress = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSearch(1); // Explicitly pass page 1
    }
  };

  useEffect(() => {
    if (tournamentDetails && tournamentDetails.tournamentCategory) {
      setSearch((prev) => ({
        ...prev,
        genderCategory:
          tournamentDetails.tournamentCategory === "open"
            ? ""
            : tournamentDetails.tournamentCategory,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tournamentDetails]);

  useEffect(() => {
    if (tournamentDetails && tournamentDetails.tournamentCategory) {
      if (
        tournamentDetails.tournamentCategory === "male" &&
        tournamentDetails.maleAgeCategory
      ) {
        setAgeCategories(tournamentDetails.maleAgeCategory);
      } else if (
        tournamentDetails.tournamentCategory === "female" &&
        tournamentDetails.femaleAgeCategory
      ) {
        setAgeCategories(tournamentDetails.femaleAgeCategory);
      }
    }
    if (search.genderCategory === "male") {
      setAgeCategories(tournamentDetails.maleAgeCategory);
    } else if (search.genderCategory === "female") {
      setAgeCategories(tournamentDetails.femaleAgeCategory);
    }
  }, [search.genderCategory, tournamentDetails]);
  useEffect(() => {
    handleSearch(1);
  }, [search.ageCategory]);

  return (
    <Paper sx={{ mb: 3, p: 2, bgcolor: "#f9f9f9" }}>
      <Box sx={{ mb: 2, display: "flex", alignItems: "center" }}>
        <Typography variant="h5" sx={{ color: "#3f51b5" }}>
          Search Players
        </Typography>
      </Box>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Player Name"
            variant="outlined"
            name="playerName"
            fullWidth
            size="small"
            value={search.playerName}
            onChange={(e) => handleInputChange("playerName", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>

        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.genderCategory}
            onChange={(e) =>
              handleInputChange("genderCategory", e.target.value)
            }
            disabled={tournamentDetails?.tournamentCategory !== "open"}
            sx={{ bgcolor: "white", textTransform: "capitalize" }}
          >
            <MenuItem value="">Gender Category</MenuItem>
            {tournamentDetails?.tournamentCategory === "open" && [
              <MenuItem key="male" value="male">
                Male
              </MenuItem>,
              <MenuItem key="female" value="female">
                Female
              </MenuItem>,
            ]}
            {tournamentDetails?.tournamentCategory === "male" && (
              <MenuItem value="male">Male</MenuItem>
            )}
            {tournamentDetails?.tournamentCategory === "female" && (
              <MenuItem value="female">Female</MenuItem>
            )}
          </Select>
        </Grid>

        <Grid item xs={12} sm={4} md={3} lg={2}>
          <Select
            fullWidth
            displayEmpty
            size="small"
            value={search.ageCategory}
            onChange={(e) => handleInputChange("ageCategory", e.target.value)}
            // renderValue={(selected) => selected || "Age Category"}
            sx={{ bgcolor: "white", color: "black" }}
          >
            <MenuItem value="">Age Category</MenuItem>
            {ageCategories.map((category, index) => (
              <MenuItem key={index} value={category}>
                {category}
              </MenuItem>
            ))}
          </Select>
        </Grid>

        {/* <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Player DOB"
            variant="outlined"
            name="dob"
            disabled={true}
            fullWidth
            size="small"
            type="date"
            value={search.playerName}
            onChange={(e) => handleInputChange("playerName", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid>

        <Grid item xs={12} sm={4} md={3} lg={2}>
          <TextField
            placeholder="Player ID"
            variant="outlined"
            name="playerId"
            fullWidth
            size="small"
            disabled={true}
            value={search.playerName}
            onChange={(e) => handleInputChange("playerId", e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ bgcolor: "white" }}
          />
        </Grid> */}

        <Grid
          item
          xs={12}
          sm={4}
          md={3}
          lg={2}
          sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}
        >
          <Button
            variant="containedSecondary"
            color="secondary"
            sx={{
              width: "40px",
              minWidth: "40px !important",
            }}
            onClick={handleReset}
            disabled={loading}
          >
            <RestartAlt />
          </Button>
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={() => handleSearch(1)}
            disabled={loading}
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SearchIcon />
              )
            }
            sx={{
              bgcolor: "#3f51b5",
              textTransform: "none",
              height: "40px",
              fontSize: "16px",
              maxWidth: {
                xs: "100%",
                sm: "150px",
              },
            }}
          >
            Search
          </Button>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PlayerSearch;
