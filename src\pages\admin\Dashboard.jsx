import { <PERSON>, Grid, <PERSON>po<PERSON>, Button } from "@mui/material";
import React from "react";
import { Link } from "react-router-dom";
import PeopleIcon from "@mui/icons-material/People";
import InfoIcon from "@mui/icons-material/Info";
import PolicyIcon from "@mui/icons-material/Gavel";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import EmailIcon from "@mui/icons-material/Email";
import SmsIcon from "@mui/icons-material/Sms";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import DescriptionIcon from "@mui/icons-material/Description";
import ReceiptIcon from "@mui/icons-material/Receipt";
import PaymentIcon from "@mui/icons-material/Payment";
import ReportIcon from "@mui/icons-material/Assessment";
import EventIcon from "@mui/icons-material/Event";
import AnalyticsIcon from "@mui/icons-material/Analytics";
import VerifiedUserIcon from "@mui/icons-material/VerifiedUser";
import SchoolIcon from "@mui/icons-material/School";
import GroupIcon from "@mui/icons-material/Groups";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import ArticleIcon from "@mui/icons-material/Article";
import AddToPhotosIcon from "@mui/icons-material/AddToPhotos";
import PolicyOutlinedIcon from "@mui/icons-material/Policy";
import AssignmentIcon from "@mui/icons-material/Assignment";
import SupervisorAccountIcon from "@mui/icons-material/SupervisorAccount";
import NotificationsIcon from "@mui/icons-material/Notifications";

const dashboardItems = [
  [
    {
      title: "Player",
      color: "#d1f1c8",
      icon: <PeopleIcon />,
      link: "players",
    },
    {
      title: "Club",
      color: "#d1f1c8",
      icon: <GroupIcon />,
      link: "clubs",
    },
    {
      title: "Arbiters",
      color: "#d1f1c8",
      icon: <VerifiedUserIcon />,
      link: "arbiters",
    },
    {
      title: "Coaches",
      color: "#d1f1c8",
      icon: <SchoolIcon />,
      link: "coaches",
    },
    {
      title: "Associations",
      color: "#d1f1c8",
      icon: <SupervisorAccountIcon />,
      link: "associations",
    },
    {
      title: "Sub Admin",
      color: "#d1f1c8",
      icon: <AdminPanelSettingsIcon />,
      link: "sub-admin",
    },
  ],
  [
    {
      title: "About Us",
      color: "#b5e3f6",
      icon: <InfoIcon />,
      link: "content-management?page=about-us",
    },
    {
      title: "Fair Play Policy",
      color: "#b5e3f6",
      icon: <PolicyOutlinedIcon />,
      link: "content-management?page=fair-play-policy",
    },
    {
      title: "Privacy Policy",
      color: "#b5e3f6",
      icon: <PolicyIcon />,
      link: "content-management?page=privacy-policy",
    },
    {
      title: "Terms & Conditions",
      color: "#b5e3f6",
      icon: <ArticleIcon />,
      link: "content-management?page=terms-conditions",
    },
    {
      title: "Refund Policy",
      color: "#b5e3f6",
      icon: <ArticleIcon />,
      link: "content-management?page=refund-policy",
    },
    {
      title: "Articles",
      color: "#b5e3f6",
      icon: <ArticleIcon />,
      link: "articles",
    },
  ],
  [
    {
      title: "Email",
      color: "#cfc0f6",
      icon: <EmailIcon />,
      link: "email",
    },
    {
      title: "SMS",
      color: "#cfc0f6",
      icon: <SmsIcon />,
      link: "sms",
    },
    {
      title: "Whatsapp",
      color: "#cfc0f6",
      icon: <WhatsAppIcon />,
      link: "whatsapp",
    },
    {
      title: "Documents",
      color: "#cfc0f6",
      icon: <DescriptionIcon />,
      link: "documents",
    },
    {
      title: "Certificates",
      color: "#cfc0f6",
      icon: <ReceiptIcon />,
      link: "certificates",
    },
    {
      title: "Pairing System",
      color: "#cfc0f6",
      icon: <AddToPhotosIcon />,
      link: "pairing-system",
    },
    {
      title: "Coaching Programs",
      color: "#cfc0f6",
      icon: <SchoolIcon />,
      link: "coaching-programs",
    },
  ],
  [
    {
      title: "Advertisements",
      color: "#fbd9f5",
      icon: <NotificationsIcon />,
      link: "add",
    },
    {
      title: "Payments",
      color: "#fbd9f5",
      icon: <PaymentIcon />,
      link: "payments",
    },
    {
      title: "Reports",
      color: "#fbd9f5",
      icon: <ReportIcon />,
      link: "reports",
    },
    {
      title: "Tournaments",
      color: "#fbd9f5",
      icon: <EventIcon />,
      link: "tournaments",
    },
    {
      title: "Google Analytics",
      color: "#fbd9f5",
      icon: <AnalyticsIcon />,
      link: "google-analytics",
    },
    {
      title: "Approvals",
      color: "#fbd9f5",
      icon: <AssignmentIcon />,
      link: "approvals",
    },
  ],
];

const AdminDashboard = () => {
  return (
    <Box sx={{ px: "5vw", py: "5vh", maxWidth: "100%" }}>
      <Typography variant="h5" fontWeight="bold" gutterBottom>
        Welcome, Uday!
      </Typography>
      <Typography
        variant="subtitle1"
        sx={{ color: "#000", textAlign: "start" }}
        gutterBottom
      >
        Super Admin Dashboard
      </Typography>

      <Grid container spacing={2}>
        {dashboardItems.map((column, colIndex) => (
          <Grid item xs key={colIndex}>
            {column.map((item, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Link to={item.link} style={{ textDecoration: "none" }}>
                  <div
                    style={{
                      backgroundColor: item.color,
                      padding: "12px 20px",
                      borderRadius: "10px",
                      boxShadow: "0 2px 5px rgba(0,0,0,0.1)",

                      width: "100%",
                      height: "100px",
                      display: "flex",
                      flexDirection: "row",
                      gap: "10px",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {item.icon}
                    <Typography
                      variant="h6"
                      sx={{
                        textWrap: "balance",
                      }}
                      fontWeight="medium"
                    >
                      {item.title}
                    </Typography>
                  </div>
                </Link>
              </Box>
            ))}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default AdminDashboard;