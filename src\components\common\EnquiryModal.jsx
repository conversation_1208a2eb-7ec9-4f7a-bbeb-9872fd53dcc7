import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import UseToast from "../../lib/hooks/UseToast";
import { Client } from "../../api/client";
import { useParams } from "react-router-dom";

// Form validation schema
const enquirySchema = z.object({
  name: z.string().min(2, "Name is required"),
  mobile: z.string().min(10, "Valid mobile number is required"),
  email: z.string().email("Valid email is required"),
  subject: z
    .string()
    .min(10, "Please provide more details about your question"),
  query: z.string().min(10, "Please provide more details about your query"),
});

const EnquiryModal = ({ open, onClose, email }) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const toast = UseToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { id: clubSlug } = useParams();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(enquirySchema),
    defaultValues: {
      name: "",
      mobile: "",
      email: "",
      subject: "",
      query: "",
      clubName: clubSlug,
    },
  });

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      const enrichedData = { ...data, clubName: clubSlug, Mail:email };
      const response = await Client.post("/club/enquiry", enrichedData);
      // await new Promise((resolve) => setTimeout(resolve, 1000));
      if (response.data.success) {
        toast.success(response.data.data.message);
        setTimeout(() => {
          reset();
        }, 1000);
      }
      setIsSuccess(true);
      reset();

      // Reset success state after 3 seconds and close modal
      setTimeout(() => {
        setIsSuccess(false);
        onClose();
      }, 3000);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit your enquiry. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      setIsSuccess(false);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullScreen={fullScreen}
      PaperProps={{
        sx: {
          bgcolor: "#000",
          color: "#fff",
          borderRadius: 2,
          maxWidth: "400px",
          width: "100%",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
        }}
      >
        <Typography variant="h5" component="div" sx={{ fontWeight: "bold" }}>
          Enquire now
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={handleClose}
          disabled={isSubmitting}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {isSuccess ? (
          <Box sx={{ py: 4, textAlign: "center" }}>
            <Typography variant="h6" fontSize={18} gutterBottom>
              Your query is submitted successfully.
            </Typography>
            <Typography variant="body1" fontSize={14}>
              We will reply to you as soon as possible.
            </Typography>
          </Box>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)}>
            <TextField
              fullWidth
              margin="dense"
              placeholder="Name"
              {...register("name")}
              error={!!errors.name}
              helperText={errors.name?.message}
              InputProps={{
                sx: { bgcolor: "#fff", borderRadius: 1 },
              }}
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              margin="dense"
              placeholder="Mobile number"
              {...register("mobile")}
              error={!!errors.mobile}
              helperText={errors.mobile?.message}
              InputProps={{
                sx: { bgcolor: "#fff", borderRadius: 1 },
              }}
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              margin="dense"
              placeholder="eMail ID"
              {...register("email")}
              error={!!errors.email}
              helperText={errors.email?.message}
              InputProps={{
                sx: { bgcolor: "#fff", borderRadius: 1 },
              }}
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              margin="dense"
              placeholder="Enter Your Query"
              {...register("subject")}
              error={!!errors.subject}
              helperText={errors.subject?.message}
              InputProps={{
                sx: { bgcolor: "#fff", borderRadius: 1 },
              }}
              disabled={isSubmitting}
            />

            <TextField
              fullWidth
              margin="dense"
              placeholder="Query"
              multiline
              rows={4}
              {...register("query")}
              error={!!errors.query}
              helperText={errors.query?.message}
              InputProps={{
                sx: { bgcolor: "#fff", borderRadius: 1 },
              }}
              disabled={isSubmitting}
            />

            <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                sx={{
                  bgcolor: "#2C832C",
                  color: "white",
                  fontSize: 16,
                  "&:hover": { bgcolor: "#236723" },
                  borderRadius: 1,
                  px: 4,
                  py: 1,
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </Box>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default EnquiryModal;
