import React, { useState, useCallback } from "react";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  PDFDownloadLink,
  PDFViewer,
} from "@react-pdf/renderer";

// Error Boundary Component for PDF debugging
class PDFErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error("PDF Error Details:", error);
    console.error("PDF Error Info:", errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            padding: "20px",
            border: "2px solid #ff4444",
            borderRadius: "8px",
            backgroundColor: "#fff5f5",
            margin: "10px 0",
          }}
        >
          <h3 style={{ color: "#cc0000", margin: "0 0 10px 0" }}>
            PDF Generation Error
          </h3>
          <p>
            <strong>Error:</strong>{" "}
            {this.state.error && this.state.error.toString()}
          </p>
          <button
            onClick={() =>
              this.setState({ hasError: false, error: null, errorInfo: null })
            }
            style={{
              marginTop: "10px",
              padding: "8px 16px",
              backgroundColor: "#007bff",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
            }}
          >
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Tournament Receipt PDF styles - Table format like original
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontFamily: "Helvetica",
  },

  // Header section
  header: {
    alignItems: "center",
    marginBottom: 20,
  },

  websiteTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
    textAlign: "center",
  },

  receiptTitle: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#000000",
    textAlign: "center",
    marginBottom: 15,
  },

  // Section styles
  section: {
    marginBottom: 15,
  },

  sectionTitle: {
    fontSize: 12,
    fontWeight: "bold",
    color: "#000000",
    marginBottom: 8,
    backgroundColor: "#f0f0f0",
    padding: 5,
  },

  // Table-like layout
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomStyle: "solid",
    borderBottomColor: "#cccccc",
    paddingVertical: 4,
    minHeight: 20,
  },

  labelColumn: {
    width: "45%",
    fontSize: 10,
    color: "#000000",
    paddingRight: 10,
  },

  valueColumn: {
    width: "55%",
    fontSize: 10,
    color: "#000000",
    paddingLeft: 10,
  },

  // Payment status highlighting
  paymentStatusRow: {
    flexDirection: "row",
    backgroundColor: "#e6f3ff",
    paddingVertical: 6,
    paddingHorizontal: 10,
    marginVertical: 5,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: "#cccccc",
  },

  paymentStatusLabel: {
    width: "45%",
    fontSize: 10,
    color: "#000000",
    fontWeight: "bold",
  },

  paymentStatusValue: {
    width: "55%",
    fontSize: 10,
    color: "#000000",
    fontWeight: "bold",
  },

  // Special formatting for certain fields
  transactionRow: {
    flexDirection: "row",
    backgroundColor: "#f9f9f9",
    paddingVertical: 4,
    paddingHorizontal: 1,
    borderBottomWidth: 1,
    borderBottomStyle: "solid",
    borderBottomColor: "#cccccc",
  },

  // Footer section
  footer: {
    marginTop: 30,
    paddingTop: 15,
    borderTopWidth: 1,
    borderTopStyle: "solid",
    borderTopColor: "#cccccc",
  },

  registrationIdText: {
    fontSize: 10,
    color: "#000000",
    textAlign: "center",
    marginBottom: 10,
  },

  footerText: {
    fontSize: 9,
    color: "#000000",
    textAlign: "center",
    marginBottom: 3,
  },

  copyright: {
    fontSize: 8,
    color: "#666666",
    textAlign: "center",
    marginTop: 5,
  },

  generatedDate: {
    fontSize: 8,
    color: "#666666",
    textAlign: "right",
    marginTop: 10,
  },
});

// Tournament Receipt PDF Document - Table format matching original
const TournamentReceiptPDF = ({ receiptData = {} }) => {
  const safeData = {
    playerInfo: {
      fullName: receiptData.playerInfo?.fullName || "Viswanathan Anand",
      email: receiptData.playerInfo?.email || "<EMAIL>",
      phone: receiptData.playerInfo?.phone || "9876543214",
      playerId: receiptData.playerInfo?.playerId || "CB25IN00007",
    },
    tournament: {
      name:
        receiptData.tournament?.name ||
        "mumbai-chess-association-championship-2",
      startDate: receiptData.tournament?.startDate || "2025-05-27",
      organizerName:
        receiptData.tournament?.organizerName || "Contact Person 2",
      organizerEmail:
        receiptData.tournament?.organizerEmail ||
        "<EMAIL>",
      organizerPhone: receiptData.tournament?.organizerPhone || "9876543213",
      location: receiptData.tournament?.location || "123 Chess Street, Mumbai",
    },
    payment: {
      tournamentRegistrationId:
        receiptData.payment?.tournamentRegistrationId || "₹1000.00",
      registrationFee: receiptData.payment?.registrationFee || "₹1000.00",
      paymentMethod: receiptData.payment?.paymentMethod || "DC",
      transactionId: receiptData.payment?.transactionId || "CB-a78b3b53",
      paymentDate: receiptData.payment?.paymentDate || "2025-05-19",
      paymentStatus: receiptData.payment?.paymentStatus || "paid",
    },
    generatedDate: new Date().toISOString(),
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.websiteTitle}>ChessBrigade.com</Text>
          <Text style={styles.receiptTitle}>
            TOURNAMENT REGISTRATION RECEIPT
          </Text>
        </View>

        {/* Player Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Player Information</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Full Name:</Text>
            <Text style={styles.valueColumn}>
              {safeData.playerInfo.fullName}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Contact Email:</Text>
            <Text style={styles.valueColumn}>{safeData.playerInfo.email}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Contact Phone:</Text>
            <Text style={styles.valueColumn}>{safeData.playerInfo.phone}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Player ID:</Text>
            <Text style={styles.valueColumn}>
              {safeData.playerInfo.playerId}
            </Text>
          </View>
        </View>

        {/* Tournament Details Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tournament Details</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Name:</Text>
            <Text style={styles.valueColumn}>{safeData.tournament.name}</Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Start Date:</Text>
            <Text style={styles.valueColumn}>
              {safeData.tournament.startDate}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Organizer Name:</Text>
            <Text style={styles.valueColumn}>
              {safeData.tournament.organizerName}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Organizer Email:</Text>
            <Text style={styles.valueColumn}>
              {safeData.tournament.organizerEmail}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>
              Tournament Organizer PhoneNumber:
            </Text>
            <Text style={styles.valueColumn}>
              {safeData.tournament.organizerPhone}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Location:</Text>
            <Text style={styles.valueColumn}>
              {safeData.tournament.location}
            </Text>
          </View>
        </View>

        {/* Payment Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Information</Text>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Tournament Registration ID:</Text>
            <Text style={styles.valueColumn}>
              {safeData.payment.tournamentRegistrationId}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Registration Fee:</Text>
            <Text style={styles.valueColumn}>
              {safeData.payment.registrationFee}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Payment Method:</Text>
            <Text style={styles.valueColumn}>
              {safeData.payment.paymentMethod}
            </Text>
          </View>

          <View style={styles.transactionRow}>
            <Text style={styles.labelColumn}>Transaction ID:</Text>
            <Text style={styles.valueColumn}>
              {safeData.payment.transactionId}
            </Text>
          </View>

          <View style={styles.tableRow}>
            <Text style={styles.labelColumn}>Payment Date:</Text>
            <Text style={styles.valueColumn}>
              {safeData.payment.paymentDate}
            </Text>
          </View>

          <View style={styles.paymentStatusRow}>
            <Text style={styles.paymentStatusLabel}>Payment Status:</Text>
            <Text style={styles.paymentStatusValue}>
              {safeData.payment.paymentStatus}
            </Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            For any queries, contact tournament Organizer.
          </Text>
          <Text style={styles.copyright}>
            © 2025 ChessBrigade.com. All rights reserved.
          </Text>
          <Text style={styles.generatedDate}>{safeData.generatedDate}</Text>
        </View>
      </Page>
    </Document>
  );
};

// Main component
const TournamentReceiptGenerator = () => {
  const [receiptData, setReceiptData] = useState({
    playerInfo: {
      fullName: "Viswanathan Anand",
      email: "<EMAIL>",
      phone: "9876543214",
      playerId: "CB25IN00007",
    },
    tournament: {
      name: "mumbai-chess-association-championship-2",
      startDate: "2025-05-27",
      organizerName: "Contact Person 2",
      organizerEmail: "<EMAIL>",
      organizerPhone: "9876543213",
      location: "123 Chess Street, Mumbai",
    },
    payment: {
      tournamentRegistrationId: "₹1000.00",
      registrationFee: "₹1000.00",
      paymentMethod: "DC",
      transactionId: "CB-a78b3b53",
      paymentDate: "2025-05-19",
      paymentStatus: "paid",
    },
  });

  const [showPreview, setShowPreview] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleDownload = useCallback(() => {
    setIsGenerating(true);
    setTimeout(() => setIsGenerating(false), 1000);
  }, []);

  const updatePlayerInfo = (field, value) => {
    setReceiptData((prev) => ({
      ...prev,
      playerInfo: { ...prev.playerInfo, [field]: value },
    }));
  };

  const updateTournamentInfo = (field, value) => {
    setReceiptData((prev) => ({
      ...prev,
      tournament: { ...prev.tournament, [field]: value },
    }));
  };

  const updatePaymentInfo = (field, value) => {
    setReceiptData((prev) => ({
      ...prev,
      payment: { ...prev.payment, [field]: value },
    }));
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
        <h1 className="text-3xl font-bold text-blue-800 mb-2">
          Tournament Receipt Generator
        </h1>
        <p className="text-gray-600 mb-6">
          Generate professional tournament registration receipts
        </p>

        {/* Controls */}
        <div className="flex gap-4 mb-6">
          <PDFErrorBoundary>
            <PDFDownloadLink
              document={<TournamentReceiptPDF receiptData={receiptData} />}
              fileName={`tournament-receipt-${receiptData.payment.transactionId}.pdf`}
              className={`px-6 py-3 rounded-lg text-white font-semibold transition-colors ${
                isGenerating
                  ? "bg-gray-500 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
              onClick={handleDownload}
            >
              {({ blob, url, loading, error }) => {
                if (loading) return "Generating PDF...";
                if (error) return `Error: ${error.message}`;
                return "📥 Download Receipt PDF";
              }}
            </PDFDownloadLink>
          </PDFErrorBoundary>

          <button
            onClick={() => setShowPreview(!showPreview)}
            className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
              showPreview
                ? "bg-red-600 hover:bg-red-700 text-white"
                : "bg-green-600 hover:bg-green-700 text-white"
            }`}
          >
            {showPreview ? "🙈 Hide Preview" : "👁️ Show Preview"}
          </button>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Form Section */}
          <div className="space-y-6">
            {/* Player Information */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800 mb-4">
                Player Information
              </h3>
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Full Name"
                  value={receiptData.playerInfo.fullName}
                  onChange={(e) => updatePlayerInfo("fullName", e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={receiptData.playerInfo.email}
                  onChange={(e) => updatePlayerInfo("email", e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <input
                  type="tel"
                  placeholder="Phone"
                  value={receiptData.playerInfo.phone}
                  onChange={(e) => updatePlayerInfo("phone", e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <input
                  type="text"
                  placeholder="Player ID"
                  value={receiptData.playerInfo.playerId}
                  onChange={(e) => updatePlayerInfo("playerId", e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Tournament Information */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-green-800 mb-4">
                Tournament Details
              </h3>
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Tournament Name"
                  value={receiptData.tournament.name}
                  onChange={(e) => updateTournamentInfo("name", e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  type="date"
                  value={receiptData.tournament.startDate}
                  onChange={(e) =>
                    updateTournamentInfo("startDate", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  type="text"
                  placeholder="Organizer Name"
                  value={receiptData.tournament.organizerName}
                  onChange={(e) =>
                    updateTournamentInfo("organizerName", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <textarea
                  placeholder="Location"
                  value={receiptData.tournament.location}
                  onChange={(e) =>
                    updateTournamentInfo("location", e.target.value)
                  }
                  rows={2}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none"
                />
              </div>
            </div>

            {/* Payment Information */}
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-lg font-semibold text-purple-800 mb-4">
                Payment Information
              </h3>
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Tournament Registration Id (e.g., ₹1000.00)"
                  value={receiptData.payment.tournamentRegistrationId}
                  onChange={(e) =>
                    updatePaymentInfo(
                      "tournamentRegistrationId",
                      e.target.value
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <input
                  type="text"
                  placeholder="Registration Fee (e.g., ₹1000.00)"
                  value={receiptData.payment.registrationFee}
                  onChange={(e) =>
                    updatePaymentInfo("registrationFee", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <select
                  value={receiptData.payment.paymentMethod}
                  onChange={(e) =>
                    updatePaymentInfo("paymentMethod", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="DC">Debit Card (DC)</option>
                  <option value="CC">Credit Card (CC)</option>
                  <option value="UPI">UPI</option>
                  <option value="NB">Net Banking (NB)</option>
                </select>
                <input
                  type="text"
                  placeholder="Transaction ID"
                  value={receiptData.payment.transactionId}
                  onChange={(e) =>
                    updatePaymentInfo("transactionId", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <input
                  type="date"
                  value={receiptData.payment.paymentDate}
                  onChange={(e) =>
                    updatePaymentInfo("paymentDate", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <select
                  value={receiptData.payment.paymentStatus}
                  onChange={(e) =>
                    updatePaymentInfo("paymentStatus", e.target.value)
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="paid">Paid</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                </select>
              </div>
            </div>
          </div>

          {/* PDF Preview */}
          {showPreview && (
            <div className="bg-white border-2 border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-100 px-4 py-2 border-b border-gray-200">
                <h3 className="font-semibold text-gray-700">📄 PDF Preview</h3>
              </div>
              <PDFErrorBoundary>
                <PDFViewer width="100%" height="800px" className="border-none">
                  <TournamentReceiptPDF receiptData={receiptData} />
                </PDFViewer>
              </PDFErrorBoundary>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TournamentReceiptGenerator;
