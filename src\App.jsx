import React from "react";
import { BrowserRouter } from "react-router-dom";
import AppRouter from "./pages/AppRoutes";
import GlobalContextProvider from "./context/GlobalContext";
import ToastProvider from "./context/ToastContext";
import { ThemeProvider } from "./theme/theme.jsx";
import ScrollToTop from "./components/common/ScrollToTop";

function App() {
  return (
    <ThemeProvider>
      <GlobalContextProvider>
        <BrowserRouter>
          <ToastProvider>
            <AppRouter />
          </ToastProvider>
          <ScrollToTop />
        </BrowserRouter>
      </GlobalContextProvider>
    </ThemeProvider>
  );
}

export default App;
