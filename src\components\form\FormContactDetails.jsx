import { Typography, Grid, Box, Stack } from "@mui/material";
import <PERSON><PERSON>extField from "./FormTextField";
import FormPhoneInput from "./FormPhoneInput";
import FormAutocomplete from "./FormAutocomplete";
import ArbiterAutocomplete from "./AribiterAutocomplete";

const FormContactDetails = ({ control, watch, arbiterIsSelected }) => {
  return (
    <>
      {" "}
      <Typography
        variant="h4"
        fontWeight="500"
        sx={{ mb: 3, textAlign: "start", fontWeight: "bold" }}
      >
        Contact Details:
      </Typography>
      <Box sx={{ mb: 4 }}>
        <Grid
          container
          spacing={4}
          sx={{ ".MuiGrid-item": { pt: "8px !important" } }}
        >
          <Grid item xs={12} md={6}>
            <FormTextField
              name="contactPersonName"
              maxLength={50}
              control={control}
              title="Contact Person Name"
              placeholder="Enter Contact Person Name"
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormTextField
              name="email"
              maxLength={100}
              control={control}
              title="Email"
              placeholder="Enter Email Address"
              required
              type="email"
              specialCharAllowed={true}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormPhoneInput
              name="contactNumber"
              control={control}
              title="Contact Number"
              placeholder="Enter Contact Number"
              required
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <FormPhoneInput
              name="alternateContactNumber"
              control={control}
              title="Alternate Contact"
              placeholder="Enter Alternate Contact"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormTextField
              name="tournamentDirectorName"
              maxLength={50}
              control={control}
              title="Tournament Director Name"
              placeholder="Enter Director Name"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {!arbiterIsSelected ? (
              <ArbiterAutocomplete
                name="chiefArbiterName"
                control={control}
                title="Chief Arbiter"
                placeholder="Search for a chief arbiter"
                required={true}
                defaultValue={{ name: null, id: null }}
                rules={{
                  required: "Please select a chief arbiter",
                }}
              />
            ) : (
              // <Typography sx={{ mt: 2 }}>
              //   Chief Arbiter: <strong>{watch("chiefArbiterName.name")}</strong>
              // </Typography>
              <FormTextField
                name="chiefArbiterName"
                maxLength={50}
                control={control}
                value={watch("chiefArbiterName.name")}
                title="Arbiter Name"
                placeholder="Enter Arbiter Name"
                required
                disabled
              />
            )}
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FormContactDetails;
