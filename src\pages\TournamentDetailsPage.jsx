import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Container,
  Al<PERSON>,
  Typo<PERSON>,
  Stack,
} from "@mui/material";
import { Link, useNavigate, useParams } from "react-router-dom";

import { Client } from "../api/client";
import UseToast from "../lib/hooks/UseToast";
import PaymentModal from "../components/payment/PaymentModal";
import CancellationModal from "../components/payment/CancellationModal";
import useGlobalContext from "../lib/hooks/UseGlobalContext";
import TournamentDetailsView from "../components/common/TournamentDetailsView";
import TournamentDetailsSkeleton from "../components/skeletons/TournamentDetailsSkeleton";
import {
  isTournamentConducted,
  isTournamentOngoing,
  isWithinRegistrationPeriod,
} from "../utils/utils";
import BackButton from "../components/common/BackButton";
import CancelIcon from "@mui/icons-material/Cancel";
import { Helmet } from "react-helmet";
import { Description } from "@mui/icons-material";

const TournamentDetailsPage = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);

  const [tournament, setTournament] = useState({});
  const [loading, setLoading] = useState(true);

  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [userDetails, setUserDetails] = useState({});

  const [isLoading, setIsLoading] = useState(false);

  const [tournamentOngoing, setTournamentOngoing] = useState(false);
  const [withinRegistrationPeriod, setWithinRegistrationPeriod] =
    useState(false);
  const [tournamentConducted, setTournamentConducted] = useState(false);
  const navigate = useNavigate();

  const { user, setOpenModel, isLoggedIn } = useGlobalContext();
  const toast = UseToast();
  const [cancellationModalOpen, setCancellationModalOpen] = useState(false);
  const [isRegistered, setIsRegistered] = useState({
    isRegistered: false,
    registrationStatus: "pending",
    registrationType: "player",
  });

  const fetchDetails = async () => {
    if (!title) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const response = await Client.get(`/tournament/${newTitle}`);
      if (response.data.success) {
        setTournament(response.data.data);

        const startDate = response.data?.data?.startDate;
        const endDate = response.data?.data?.endDate;
        const reportingTime = response.data?.data?.reportingTime;

        const ongoing = isTournamentOngoing(startDate, endDate, reportingTime);
        setTournamentOngoing(ongoing);

        const start = response.data?.data?.registrationStartDate;
        const end = response.data?.data?.registrationEndDate;
        const endTime = response.data?.data?.registrationEndTime;

        const isOpen = isWithinRegistrationPeriod(start, end, endTime);
        setWithinRegistrationPeriod(isOpen);
        const conducted = isTournamentConducted(endDate);
        setTournamentConducted(conducted);
      } else {
        toast.info(
          response.data.message || "Failed to load tournament details"
        );
      }
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("Error fetching tournament details. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDetails();
  }, [newTitle, user]);
  const getFileExtension = (mimeType) => {
    const extensions = {
      "image/png": ".png",
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "text/plain": ".txt",
      "application/vnd.ms-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
    };
    return extensions[mimeType] || "";
  };

  const handleBrochureDownload = async ({ id, title }) => {
    try {
      // Request a short-lived pre-signed URL from your backend
      const response = await Client.get(`/tournament/${id}/brochure`);
      const presignedUrl = response.data.data;

      if (!presignedUrl) {
        throw new Error("No download URL received from server");
      }

      // Use the pre-signed URL (typically valid for 5-15 minutes)
      const fileResponse = await fetch(presignedUrl);

      if (!fileResponse.ok) {
        throw new Error(
          ` Failed to download file: ${fileResponse.status} ${fileResponse.statusText}`
        );
      }

      const blob = await fileResponse.blob();

      if (blob.size === 0) {
        throw new Error("Downloaded file is empty");
      }

      // Sanitize filename to remove invalid characters
      const sanitizedTitle =
        title.replace(/[<>:"/\\|?*]/g, "").trim() || "download";

      // Get extension from Content-Type header first, then fallback to blob type
      const contentType = fileResponse.headers.get("content-type");
      const extension =
        getFileExtension(contentType) || getFileExtension(blob.type) || "";

      // Download the file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", sanitizedTitle + "_Brochure" + extension);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
      // You might want to show a user-friendly error message here
      throw error;
    }
  };

  const handleRemindMe = async () => {
    if (!isLoggedIn) {
      // Redirect to login or show login modal
      setOpenModel((prev) => ({ ...prev, login: true }));
      return;
    }

    try {
      setIsLoading(true);
      const response = await Client.post(
        `/player/tournaments/${tournament.title}/reminder`
      );
      if (!response.data.success) {
        toast.error(response.data.message);
        return;
      }

      toast.success(response.data.data.message);
    } catch (error) {
      toast.error(error.response.data.error || "Unable to set reminder");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    const checkRegistrationStatus = async () => {
      if (
        !user ||
        !title ||
        !tournament ||
        !tournament.id ||
        user?.role !== "player"
      )
        return;

      try {
        const response = await Client.get(
          `/tournament/${newTitle}/register/status`,
          { params: { tournamentId: tournament.id } }
        );

        if (response.data.success) {
          const { registrationStatus, registrationType } = response.data.data;

          setIsRegistered({
            isRegistered: registrationStatus === "active",
            registrationStatus,
            registrationType,
          });
        }
      } catch (error) {
        console.error("Error checking registration status:", error);
      }
    };

    if (tournament.id) {
      checkRegistrationStatus();
    }
  }, [user, title, tournament.id, tournament]);

  // Handle tournament registration button click
  const handleRegister = async () => {
    if (!user) {
      toast.info("Please login to register for the tournament");
      setOpenModel((prev) => ({ ...prev, login: true }));
      localStorage.setItem("navigateTo", false);
      return false;
    }
    // Check eligibility before registration
    const getUser = async () => {
      try {
        // Call the eligibility check endpoint
        const response = await Client.get("/player/profile");

        if (response.status === 204) {
          toast.info("You haven't created a profile yet.");
          localStorage.setItem("navigateTo", `/tournaments/${title}`);
          setTimeout(() => {
            navigate("/dashboard/profile/edit?edit=0");
          }, 300);

          return false;
        }

        if (response.data.success) {
          setUserDetails(response.data.data);
          if (
            tournament.tournamentCategory === "female" &&
            response.data.data.gender !== "female"
          ) {
            toast.error(
              `Only ${tournament.tournamentCategory} players can register for this tournament.`
            );
            return false;
          }
          return true;
        }
        return false;
      } catch (error) {
        if (error.response?.data?.error) {
          toast.error(error.response.data.error);
        }
        return false;
      }
    };
    const isEligible = await getUser();
    if (!isEligible) return;
    setPaymentModalOpen(true);
  };
  const handleOpenCancellationModal = () => {
    setCancellationModalOpen(true);
  };

  return (
    <>
      <Helmet>
        <title>{`${title} | Chessbrigade.com`}</title>
        <meta
          name="description"
          content="Register now and showcase your chess skills in top tournaments!"
        />
        <link
          rel="canonical"
          href={`https://www.chessbrigade.com/tournaments/${title}`}
        />

        {/* Open Graph Meta Tags for Social Media */}
        <meta property="og:title" content={`${title} | Chessbrigade.com`} />
        <meta
          property="og:description"
          content="Register now and showcase your chess skills in top tournaments!"
        />
        <meta property="og:type" content="website" />
        <meta
          property="og:url"
          content={`https://www.chessbrigade.com/tournaments/${title}`}
        />
        <meta
          property="og:image"
          content="https://www.chessbrigade.com/favicon.svg"
        />
        <meta property="og:site_name" content="ChessBrigade" />

        {/* Twitter Card Meta Tags */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={`${title} | Chessbrigade.com`} />
        <meta
          name="twitter:description"
          content="Register now and showcase your chess skills in top tournaments!"
        />
     

        {/* WhatsApp specific (uses Open Graph) */}
        <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />
      </Helmet>
      {loading ? (
        <TournamentDetailsSkeleton />
      ) : (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
          {/* Payment Success Alert */}
          <BackButton />

          <div style={{ minHeight: "100vh" }}>
            {tournament && <TournamentDetailsView tournaments={tournament} />}
          </div>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "center",
              flexDirection: "row",
              gap: 2,
              p: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            {(!user || (user && user?.role === "player")) && (
              <Stack
                spacing={2}
                alignItems="center"
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: 2,
                }}
              >
                {isRegistered.registrationStatus === "active" && (
                  <Alert
                    sx={{
                      fontSize: "1rem",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 2,
                      p: 1,
                      "& .MuiAlert-message": { p: "0px !important" },
                    }}
                    severity="success"
                    icon={false}
                  >
                    You are registered for this tournament.
                  </Alert>
                )}
                {isRegistered.registrationStatus === "cancelled" &&
                  isRegistered.registrationType === "player" && (
                    <Alert
                      icon={false}
                      sx={{
                        fontSize: "1rem",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        gap: 2,
                        p: 1,
                        "& .MuiAlert-message": { p: "0px !important" },
                      }}
                      severity="error"
                    >
                      Registration cancelled, refund processed.
                    </Alert>
                  )}
                {isRegistered?.registrationStatus === "refunded" &&
                  isRegistered?.registrationType === "player" && (
                    <Alert
                      sx={{
                        fontSize: "1rem",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        gap: 2,
                        p: 1,
                        "& .MuiAlert-message": { p: "0px !important" },
                      }}
                      icon={false}
                      severity="info"
                    >
                      Refund completed.
                    </Alert>
                  )}

                {/* Show Cancel button only for active */}
                {isRegistered?.isRegistered &&
                  isRegistered?.registrationStatus === "active" &&
                  isRegistered?.registrationType === "player" &&
                  !tournamentOngoing &&
                  !tournamentConducted && (
                    <Button
                      onClick={handleOpenCancellationModal}
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        maxWidth: "250px",
                        px: 2,
                        mt: "0px !important",
                      }}
                    >
                      Cancel Registration
                    </Button>
                  )}

                {/* Allow re-registration for cancelled/refunded */}
                {(!user || (user && !isRegistered?.isRegistered)) &&
                  withinRegistrationPeriod &&
                  !tournamentOngoing &&
                  !tournamentConducted && (
                    <Button
                      onClick={handleRegister}
                      sx={{
                        borderRadius: 1,
                        textTransform: "none",
                        fontWeight: 500,
                        fontSize: 16,
                        maxWidth: "250px",
                        px: 2,
                        bgcolor: "#166DA3", // Default background
                        color: "#fff", // Text color for good contrast

                        "&:hover": {
                          bgcolor: "#1A7FBF", // Lighter blue on hover
                          boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(-2px)",
                          transition: "all 0.3s ease",
                        },

                        "&:active": {
                          bgcolor: "#125B87", // Slightly darker blue for active click
                          boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                          transform: "translateY(0)", // Reset push on click
                        },
                        mt: "0px !important",
                      }}
                    >
                      Register Now
                    </Button>
                  )}
              </Stack>
            )}
            {user && user?.role === "club" && (
              <>
                {withinRegistrationPeriod && (
                  <Button
                    variant="contained"
                    color="success"
                    fullWidth
                    href={`/dashboard/tournaments/${newTitle}/bulk-registration`}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      maxWidth: "250px",
                      px: 2,
                      bgcolor: "#166DA3", // Default background

                      "&:hover": {
                        bgcolor: "#1A7FBF", // Lighter blue on hover
                        boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(-2px)",
                        transition: "all 0.3s ease",
                      },

                      "&:active": {
                        bgcolor: "#125B87", // Slightly darker blue for active click
                        boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(0)", // Reset push on click
                      },
                    }}
                  >
                    Register Players
                  </Button>
                )}
              </>
            )}
          </Box>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: 2,
              p: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            {tournament.locationUrl &&
              tournament.locationUrl.startsWith("http") && (
                <Button
                  variant="contained"
                  color="success"
                  fullWidth
                  type="a"
                  target="_blank"
                  rel="noopener noreferrer"
                  href={
                    tournament && tournament.locationUrl
                      ? tournament.locationUrl
                      : "#"
                  }
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    maxWidth: "250px",
                    px: 2,
                    bgcolor: "#166DA3", // Default background

                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },

                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  View Map
                </Button>
              )}
            {tournament?.chatUrl &&
              tournament?.chatUrl.startsWith("http") &&
              !tournamentConducted && (
                <Button
                  variant="contained"
                  color="success"
                  fullWidth
                  onClick={() => {
                    if (!isLoggedIn) {
                      toast.info("Please login to chat");
                      setOpenModel((prev) => ({ ...prev, login: true }));
                      return;
                    }

                    window.open(tournament?.chatUrl, "_blank");
                  }}
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    maxWidth: "250px",
                    px: 2,
                    bgcolor: "#166DA3", // Default background

                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },

                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Chat Now
                </Button>
              )}
            {tournament.brochureUrl && (
              <Button
                variant="contained"
                fullWidth
                onClick={() =>
                  handleBrochureDownload({
                    id: tournament.id,
                    title: tournament.title,
                  })
                }
                color="success"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  maxWidth: "250px",
                  px: 2,
                  bgcolor: "#166DA3", // Default background
                  color: "#fff", // Text color for good contrast

                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },

                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Download Brochure
              </Button>
            )}
            {new Date() <= new Date(tournament.registrationStartDate) && (
              <Button
                fullWidth
                onClick={handleRemindMe}
                disabled={isLoading}
                color="primary"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  color: "white",
                  px: 2,
                  maxWidth: "250px",
                  bgcolor: "#166DA3", // Default background

                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },

                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Remind me
              </Button>
            )}

            {new Date(tournament.registrationStartDate) <= new Date() && (
              <>
                <Button
                  fullWidth
                  component={Link}
                  to="registered-players"
                  color="primary"
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    color: "white",
                    px: 2,
                    maxWidth: "250px",
                    bgcolor: "#166DA3", // Default background

                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },

                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Registered Players
                </Button>

                <Button
                  fullWidth
                  component={Link}
                  to="pairing-details"
                  color="primary"
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    color: "white",
                    px: 2,
                    maxWidth: "250px",
                    bgcolor: "#166DA3", // Default background

                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },

                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Pairing Details
                </Button>
              </>
            )}

            {(tournamentOngoing || tournamentConducted) && (
              <Button
                fullWidth
                component={Link}
                to="leader-board"
                color="primary"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  color: "white",
                  px: 2,
                  maxWidth: "250px",
                  bgcolor: "#166DA3", // Default background

                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },

                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Leader Board
              </Button>
            )}

            {/* Payment Modal */}
            {user && user?.role === "player" && (
              <>
                <PaymentModal
                  open={paymentModalOpen}
                  setLoading={setLoading}
                  onClose={() => setPaymentModalOpen(false)}
                  tournament={tournament}
                  userDetails={userDetails}
                />
                {isRegistered.isRegistered &&
                  isRegistered.registrationStatus === "active" &&
                  isRegistered.registrationType === "player" && (
                    <CancellationModal
                      open={cancellationModalOpen}
                      onClose={() => setCancellationModalOpen(false)}
                      tournament={tournament}
                      onsuccess={() => fetchDetails()}
                    />
                  )}
              </>
            )}
          </Box>
        </Container>
      )}
    </>
  );
};

export default TournamentDetailsPage;
