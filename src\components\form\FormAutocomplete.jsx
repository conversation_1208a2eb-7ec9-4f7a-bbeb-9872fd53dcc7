import React from "react";
import { Typography, Autocomplete, TextField, Fade } from "@mui/material";
import { Controller } from "react-hook-form";

// Custom Autocomplete component that works with React Hook Form
const FormAutocomplete = React.memo(
  ({
    name,
    control,
    options,
    getOptionLabel,
    placeholder,
    maxLength,

    title,
    sx,
    required = false,
    disabled = false,
    rules,
    ...rest
  }) => {
    return (
      <>
        <Typography
          variant="h6"
          sx={{
            textAlign: "start",
            p: "0px !important",
            m: "0px !important",
          }}
        >
          {title}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({
            field: { onChange, value, ref, ...field },
            fieldState: { error },
          }) => (
            <Autocomplete
              options={options}
              getOptionLabel={getOptionLabel}
              value={
                value
                  ? options.find(
                      (option) => getOptionLabel(option) === value
                    ) || null
                  : null
              }
              sx={{
                "& .MuiInputBase-root": { p: "0px 5px !important " },
                ...sx,
              }}
              onChange={(_, newValue) => {
                onChange(newValue ? getOptionLabel(newValue) : "");
              }}
              disabled={disabled}
              renderInput={(params) => (
                <TextField
                  {...params}
                  {...field}
                  inputRef={ref}
                  placeholder={placeholder}
                  variant="outlined"
                  margin="normal"
                  error={!!error}
                  helperText={
                    <Fade in={!!error}>
                      <Typography
                        component="span"
                        variant="caption"
                        color="error"
                      >
                        {error?.message}
                      </Typography>
                    </Fade>
                  }
                />
              )}
              {...rest}
            />
          )}
        />
      </>
    );
  }
);

export default FormAutocomplete;
