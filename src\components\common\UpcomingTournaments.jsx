import {
  Box,
  Button,
  Divider,
  Paper,
  Skeleton,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { Client } from "../../api/client";
import CustomPagination from "./CustomPagination";
import { Link } from "react-router-dom";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const UpcomingTournaments = ({ club = false, arbiter = false }) => {
  const [tournamentData, setTournamentData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 0,
    total: 0,
  });
  const { user } = UseGlobalContext();
  const fetchTounaments = async () => {
    setLoading(true);
    try {
      if (club) {
        const response = await Client.get(`/tournament/club/upcoming`, {
          params: { limit: 3, page: pagination.page },
        });

        setPagination({
          ...pagination,
          page: Number(response.data.data.currentPage),
          totalPages: Number(response.data.data.totalPages),
          total: Number(response.data.data.total),
        });
        setTournamentData(response.data.data.tournaments);
      } else if (arbiter) {
        const response = await Client.get(`/arbiter/profile/tournaments`, {
          params: { limit: 3, page: pagination.page },
        });

        setPagination({
          ...pagination,
          page: Number(response.data.data.currentPage),
          totalPages: Number(response.data.data.totalPages),
          total: Number(response.data.data.total),
        });
        setTournamentData(response.data.data.tournaments);
      } else {
        const response = await Client.get(`/player/profile/tournaments`, {
          params: { limit: 3, page: pagination.page },
        });

        setPagination({
          ...pagination,
          page: Number(response.data.data.currentPage),
          totalPages: Number(response.data.data.totalPages),
          total: Number(response.data.data.total),
        });
        setTournamentData(response.data.data.tournaments);
      }
    } catch (error) {
      console.error("Error fetching tournaments:", error);
    } finally {
      setLoading(false);
    }
  };
  // Initial data fetch
  useEffect(() => {
    fetchTounaments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination.page]);

  return (
    <Box sx={{ mt: 4 }}>
      <TableContainer component={Paper} sx={{ mt: 2 }}>
        <Table>
          <TableHead sx={{ bgcolor: "#CCBEF033" }}>
            <TableRow>
              <TableCell>Upcoming Tournaments</TableCell>

              <TableCell align="right"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              // Loading skeletons - always show 3
              Array(3)
                .fill(0)
                .map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    <TableCell colSpan={2}>
                      <Skeleton variant="rectangular" height={30} />
                    </TableCell>
                  </TableRow>
                ))
            ) : tournamentData.length === 0 ? (
              // No tournaments found - show message in first row and empty rows for the rest
              <>
                <TableRow sx={{ bgcolor: "#BEDDF026" }}>
                  <TableCell colSpan={2} align="center">
                    No tournaments found{" "}
                    {user?.role === "club" && (
                      <Link to="dashboard/createTournament">
                        Create a new tournament
                      </Link>
                    )}
                  </TableCell>
                </TableRow>
                {Array(2)
                  .fill(0)
                  .map((_, index) => (
                    <TableRow
                      key={`empty-${index}`}
                      sx={{ bgcolor: "#BEDDF026" }}
                    >
                      <TableCell
                        colSpan={2}
                        sx={{
                          height: "53px",
                          borderBottom: index === 1 ? "none" : undefined,
                        }}
                      >
                        &nbsp;
                      </TableCell>
                    </TableRow>
                  ))}
              </>
            ) : (
              // Show available tournaments and fill remaining slots with empty rows
              <>
                {tournamentData.map((tournament, index) => {
                  const isLastTournament = index === tournamentData.length - 1;
                  const isLastRow =
                    isLastTournament && tournamentData.length === 3;

                  return (
                    <TableRow
                      key={`tournament-${index}`}
                      sx={{ bgcolor: "#BEDDF026" }}
                    >
                      <TableCell
                        sx={{
                          height: "53px",
                          borderBottom: isLastRow ? "none" : undefined,
                          textTransform: "capitalize",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          maxWidth: "300px",
                        }}
                      >
                        {tournament.title.toUpperCase().replace(/-/g, " ")}
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottom: isLastRow ? "none" : undefined,
                        }}
                      >
                        <Button
                          variant="contained"
                          component={Link}
                          to={
                            user?.role === "club" || user?.role === "arbiter"
                              ? `/dashboard/tournaments/${encodeURIComponent(
                                  tournament.title
                                )}`
                              : `/tournaments/${encodeURIComponent(
                                  tournament.title
                                )}`
                          }
                          size="small"
                          sx={{
                            bgcolor: "#2c2891",
                            color: "white",
                            textTransform: "none",
                            textAlign: "center",
                          }}
                        >
                          View Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
                {/* Add empty rows to always have 3 rows total */}
                {tournamentData.length < 3 &&
                  Array(3 - tournamentData.length)
                    .fill(0)
                    .map((_, index) => {
                      const isLastRow = index === 3 - tournamentData.length - 1;
                      return (
                        <TableRow
                          key={`empty-${index}`}
                          sx={{ bgcolor: "#BEDDF026" }}
                        >
                          <TableCell
                            colSpan={2}
                            sx={{
                              height: "53px",
                              borderBottom: isLastRow ? "none" : undefined,
                            }}
                          >
                            &nbsp;
                          </TableCell>
                        </TableRow>
                      );
                    })}
              </>
            )}
            <TableRow>
              <TableCell colSpan={2} sx={{ p: 0, borderBottom: "none" }}>
                <Divider />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
        <CustomPagination
          totalPages={pagination.totalPages}
          currentPage={pagination.page}
          onPageChange={(page) => setPagination({ ...pagination, page })}
          sx={{ bgcolor: "#BEDDF026", justifyContent: "flex-end" }}
        />
      </TableContainer>
    </Box>
  );
};

export default UpcomingTournaments;
