import BankBuildingIcon from "@mui/icons-material/AccountBalance";
import PlusIcon from "@mui/icons-material/Add";
import PersonalTrainerIcon from "@mui/icons-material/FitnessCenter";
import MemberIcon from "@mui/icons-material/People";
import AddUserMaleIcon from "@mui/icons-material/PersonAdd";
import PaymentHistoryIcon from "@mui/icons-material/Receipt";
import TrainingIcon from "@mui/icons-material/SportsHandball";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import { Box, Grid, Paper, Typography, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import LinkItemDashboard from "../../components/common/LinkItemDashboard";
import UpcomingTournaments from "../../components/common/UpcomingTournaments";
import { Hub, Message, Notifications } from "@mui/icons-material";
import { Client } from "../../api/client";
// Dashboard card data for mapping
const dashboardCards = [
  {
    title: "Club Profile",
    color: "#ebccff",
    icon: <AddUserMaleIcon />,
    link: "profile",
  },
  {
    title: "Notifications",
    color: "#FFD1DC",
    icon: <Notifications />,
    link: "notifications",
  },
  { title: "Members", color: "#fac67c", icon: <MemberIcon />, link: "members" },
  {
    title: "Banking Details",
    color: "#e7d17b",
    icon: <BankBuildingIcon />,
    link: "bankingdetails",
  },
  {
    title: "Tournament Earnings",
    color: "#a5d8c6",
    icon: <PaymentHistoryIcon />,
    link: "tournaments-earnings",
  },
  {
    title: "Payment History",
    color: "#beddf0",
    icon: <PaymentHistoryIcon />,
    link: "payment-history",
  },
  {
    title: "Coaches",
    color: "#f3beb9",
    icon: <PersonalTrainerIcon />,
    link: "coaches",
  },
  {
    title: "Coaching",
    color: "#f0e6be",
    icon: <TrainingIcon />,
    link: "coaching",
  },
  {
    title: "Pairing System",
    color: "#c4b8f0",
    icon: <Hub />,
    link: "pairing-system",
  },
  {
    title: "Send Messages",
    color: "#fad0e8",
    icon: <Message />,
    link: "message",
  },
];

// Tournament section cards
const tournamentCards = [
  {
    title: "Create tournament",
    color: "#a5d8c6",
    icon: <PlusIcon sx={{ fontSize: "30px", mx: 2 }} />,
    link: "createTournament",
  },
  {
    title: "Upcoming Tournaments",
    color: "#302DA099",
    icon: null,
    link: "tournaments?status=upcoming",
  },
  {
    title: "Tournaments in Progress",
    color: "#8B8B90A6",
    icon: null,
    link: "tournaments?status=in-progress",
  },
  {
    title: "Tournaments Conducted",
    color: "#FBCCD5",
    icon: null,
    link: "tournaments?status=completed",
  },
];

const ClubDashboard = () => {
  const { user, currentProfile, fetchProfileData, authLoading } =
    useGlobalContext();
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = useState([]);

  const getAd = async () => {
    setLoading(true);
    try {
      const response = await Client.get("/user/ad/club");
      if (response.data.success) {
        setData(response.data.data);
      }
    } catch (e) {
      console.error("error while get ad", e);
    } finally {
      setLoading(false);
    }
  };
  // Fetch profile data if not already available
  useEffect(() => {
    const getProfileData = async () => {
      if (user && !currentProfile && !authLoading) {
        setLoading(true);
        try {
          await fetchProfileData();
        } catch (error) {
          console.error("Error fetching club profile:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    getProfileData();
    getAd();
  }, [user, currentProfile, authLoading]);

  return (
    <Box sx={{ px: "5vw", py: "5vh", maxWidth: "100%" }}>
      {loading ? (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          height="50vh"
        >
          <CircularProgress />
        </Box>
      ) : (
        <>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Welcome, {user?.name || "back"}!
          </Typography>

          <Typography variant="h4" gutterBottom sx={{ mt: 2 }}>
            Club Dashboard
          </Typography>

          <LinkItemDashboard links={dashboardCards} Data={data} />

          {/* Tournament Cards */}
          <Grid container spacing={3} sx={{ mt: 4 }}>
            {tournamentCards.map((card, index) => {
              return (
                <Grid item xs={12} md={3} key={index}>
                  <Link to={card.link} aria-disabled={card.link === "#"}>
                    <Paper
                      elevation={4}
                      sx={{
                        bgcolor: card.color,
                        p: 2,
                        borderRadius: 3,

                        py: 3,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                      }}
                    >
                      <Typography
                        variant="h5"
                        sx={{
                          ml: 2,
                          textWrap: "balance",
                          display: "flex",
                          alignItems: "center",
                        }}
                        fontWeight="medium"
                      >
                        {card.title} {card.icon}
                      </Typography>
                    </Paper>
                  </Link>
                </Grid>
              );
            })}
          </Grid>
          <UpcomingTournaments club={true} />
        </>
      )}
    </Box>
  );
};

export default ClubDashboard;
