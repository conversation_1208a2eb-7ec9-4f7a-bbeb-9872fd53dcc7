import { Client } from "../../api/client";

/**
 * Payout Service - Handles all payout-related API calls
 */

/**
 * Setup club for payouts (one-time setup)
 */
export const setupClubForPayouts = async () => {
  try {
    const response = await Client.post("/payout/setup");
    return response.data;
  } catch (error) {
    console.error("Error setting up club for payouts:", error);
    throw error;
  }
};

/**
 * Calculate payout preview for a tournament
 * @param {string} tournamentId - Tournament ID
 * @param {Object} options - Additional options for calculation
 */
export const calculatePayoutPreview = async (tournamentId, options = {}) => {
  try {
    const response = await Client.post(`/payout/calculate/${encodeURIComponent(tournamentId)}`, options);
    return response.data;
  } catch (error) {
    console.error("Error calculating payout preview:", error);
    throw error;
  }
};

/**
 * Create a payout for a tournament
 * @param {Object} payoutData - Payout creation data
 */
export const createPayout = async (payoutData) => {
  try {
    const response = await Client.post("/payout/create", payoutData);
    return response.data;
  } catch (error) {
    console.error("Error creating payout:", error);
    throw error;
  }
};

/**
 * Get payout status by payout ID
 * @param {string} payoutId - Payout ID
 */
export const getPayoutStatus = async (payoutId) => {
  try {
    const response = await Client.get(`/payout/status/${payoutId}`);
    return response.data;
  } catch (error) {
    console.error("Error getting payout status:", error);
    throw error;
  }
};

/**
 * Get list of payouts for a club
 * @param {Object} params - Query parameters
 */
export const getClubPayouts = async (params = {}) => {
  try {
    const response = await Client.get("/payout/list", { params });
    return response.data;
  } catch (error) {
    console.error("Error getting club payouts:", error);
    throw error;
  }
};

/**
 * Get tournament data for payout calculations
 * @param {string} tournamentId - Tournament ID
 */
export const getTournamentData = async (tournamentId) => {
  try {
    const response = await Client.get(`/admin/tournament/${encodeURIComponent(tournamentId)}`);
    return response.data;
  } catch (error) {
    console.error("Error getting tournament data:", error);
    throw error;
  }
};

/**
 * Utility functions for payout status handling
 */

/**
 * Get status color for payout status
 * @param {string} status - Payout status
 */
export const getPayoutStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'processed':
    case 'completed':
      return 'success';
    case 'pending':
    case 'queued':
      return 'warning';
    case 'failed':
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

/**
 * Get status text for display
 * @param {string} status - Payout status
 */
export const getPayoutStatusText = (status) => {
  switch (status?.toLowerCase()) {
    case 'processed':
      return 'Processed';
    case 'completed':
      return 'Completed';
    case 'pending':
      return 'Pending';
    case 'queued':
      return 'Queued';
    case 'failed':
      return 'Failed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status || 'Unknown';
  }
};

/**
 * Format currency amount for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: INR)
 */
export const formatCurrency = (amount, currency = 'INR') => {
  if (!amount && amount !== 0) return 'N/A';
  
  const symbol = currency === 'INR' ? '₹' : currency;
  return `${symbol}${parseFloat(amount).toLocaleString(undefined, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })}`;
};

/**
 * Calculate payout percentage
 * @param {number} payoutAmount - Payout amount
 * @param {number} totalAmount - Total collected amount
 */
export const calculatePayoutPercentage = (payoutAmount, totalAmount) => {
  if (!totalAmount || totalAmount === 0) return 0;
  return ((payoutAmount / totalAmount) * 100).toFixed(2);
};

/**
 * Validate payout data before submission
 * @param {Object} payoutData - Payout data to validate
 */
export const validatePayoutData = (payoutData) => {
  const errors = [];
  
  if (!payoutData.tournament_id) {
    errors.push('Tournament ID is required');
  }
  
  if (!payoutData.club_id) {
    errors.push('Club ID is required');
  }
  
  if (payoutData.custom_amount && payoutData.custom_amount <= 0) {
    errors.push('Custom amount must be greater than 0');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
