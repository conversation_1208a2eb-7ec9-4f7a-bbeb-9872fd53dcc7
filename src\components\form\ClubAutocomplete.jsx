import React, { useState, useEffect, useCallback } from "react";
import {
  Typography,
  Autocomplete,
  TextField,
  Fade,
  CircularProgress,
  Box,
} from "@mui/material";
import { Controller } from "react-hook-form";
import { Client } from "../../api/client";

const ClubAutocomplete = ({
  name,
  control,
  placeholder = "Search for Clubs or type custom club name",
  title = "Clubs",
  sx = {},
  required = false,
  disabled = false,
  rules = {},
  fetchUrl = "/club/get",
  defaultValue = null,
  setValue,
  watch,
  prefetch = null,
  edit = false,
}) => {
  const [clubs, setClubs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [open, setOpen] = useState(false);

  // Watch for form values
  const currentOtherClubs = watch ? watch("other_clubs") : false;
  const currentClub = watch ? watch("club") : "";
  const currentClubs = watch ? watch("clubs") : {};

  // Initialize component based on prefetch data or edit mode
  useEffect(() => {
    if (edit && prefetch) {
      // Edit mode - check existing club data
      if (prefetch.other_clubs && prefetch.club) {
        // User has custom club
        setValue("other_clubs", true);
        setValue("club", prefetch.club);
        setValue("clubs", {});
      } else if (prefetch.clubs && prefetch.clubs.clubId) {
        // User has regular club
        setValue("other_clubs", false);
        setValue("clubs", prefetch.clubs);
        setValue("club", "");
      } else if (!prefetch.clubId) {
        // No existing club data - start with autocomplete
        setValue("other_clubs", false);
        setValue("club", "");
        setValue("clubs", {});
      }
    } else {
      // Create mode - start with autocomplete visible
      setValue("other_clubs", false);
      setValue("club", "");
      setValue("clubs", {});
    }
  }, [prefetch, edit, setValue]);

  // Memoize the fetch function
  const fetchClubs = useCallback(
    async (searchTerm) => {
      if (!searchTerm || searchTerm.trim() === "") {
        setClubs([{ id: "others", clubId: "others", clubName: "Others" }]);
        return;
      }

      setLoading(true);
      try {
        const response = await Client.get(
          `${fetchUrl}/${encodeURIComponent(searchTerm)}`
        );
        const clubsData = response.data.data || [];
        // Always add "Others" option at the end
        setClubs([
          ...clubsData,
          { id: "others", clubId: "others", clubName: "Others" },
        ]);
      } catch (error) {
        console.error("Error fetching clubs:", error);
        setClubs([{ id: "others", clubId: "others", clubName: "Others" }]);
      } finally {
        setLoading(false);
      }
    },
    [fetchUrl]
  );

  // Debounced search
  useEffect(() => {
    if (!currentOtherClubs) {
      const debounceTimer = setTimeout(() => {
        fetchClubs(inputValue);
      }, 300);

      return () => clearTimeout(debounceTimer);
    }
  }, [inputValue, fetchClubs, currentOtherClubs]);

  // Handle custom club input change
  const handleCustomClubChange = (value) => {
    setValue("club", value);
    setValue("other_clubs", value !== "");
    setValue("clubs", {});

    if (value === "") {
      // If cleared, switch back to autocomplete
      setValue("other_clubs", false);
    }
  };

  // Handle autocomplete selection
  const handleAutocompleteChange = (newValue) => {
    if (newValue?.clubId === "others") {
      // Switch to custom mode
      setValue("other_clubs", true);
      setValue("club", "");
      setValue("clubs", {});
    } else if (newValue) {
      // Regular club selected
      const clubValue = {
        id: newValue.id || newValue.clubId,
        clubName: newValue.clubName,
        clubId: newValue.clubId || newValue.id,
      };
      setValue("clubs", clubValue);
      setValue("club", "");
      setValue("other_clubs", false);
    } else {
      // No selection (cleared)
      setValue("clubs", {});
      setValue("club", "");
      setValue("other_clubs", false);
    }
  };

  // Get current display value for autocomplete
  const getCurrentValue = () => {
    if (currentOtherClubs) {
      return null; // Don't show value in autocomplete when in custom mode
    }
    return currentClubs && currentClubs.clubId ? currentClubs : null;
  };

  return (
    <>
      <Typography
        variant="h6"
        sx={{
          textAlign: "start",
          p: "0px !important",
          m: "0px !important",
        }}
      >
        {title}
        {required && <span style={{ color: "red" }}>*</span>}
      </Typography>

      {!currentOtherClubs ? (
        <Controller
          name="clubs"
          control={control}
          rules={rules}
          render={({ field, fieldState: { error } }) => (
            <Autocomplete
              options={clubs}
              getOptionLabel={(option) => option?.clubName || ""}
              value={getCurrentValue()}
              sx={{
                "& .MuiInputBase-root": { p: "0px 5px !important" },
                color: "black",
                ...sx,
              }}
              onChange={(_, newValue) => handleAutocompleteChange(newValue)}
              onInputChange={(_, newInputValue) => {
                setInputValue(newInputValue);
              }}
              open={open}
              onOpen={() => {
                setOpen(true);
                if (clubs.length === 0) {
                  fetchClubs("");
                }
              }}
              onClose={() => setOpen(false)}
              isOptionEqualToValue={(option, value) =>
                option?.clubId === value?.clubId
              }
              loading={loading}
              disabled={disabled}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder={placeholder}
                  variant="outlined"
                  margin="normal"
                  error={!!error}
                  InputProps={{
                    ...params.InputProps,
                    endAdornment: (
                      <>
                        {loading ? (
                          <CircularProgress color="inherit" size={20} />
                        ) : null}
                        {params.InputProps.endAdornment}
                      </>
                    ),
                  }}
                  helperText={
                    error ? (
                      <Fade in={!!error}>
                        <Typography
                          component="span"
                          variant="caption"
                          color="error"
                        >
                          {error?.message}
                        </Typography>
                      </Fade>
                    ) : null
                  }
                />
              )}
              renderOption={(props, option) => (
                <li {...props} key={option.clubId || option.id}>
                  {option.clubName}
                </li>
              )}
            />
          )}
        />
      ) : (
        <Box sx={{ mt: 1 }}>
          <TextField
            fullWidth
            placeholder="Enter your Custom Club Name"
            value={currentClub}
            onChange={(e) => handleCustomClubChange(e.target.value)}
            variant="outlined"
            margin="normal"
            disabled={disabled}
          />
        </Box>
      )}
    </>
  );
};

export default ClubAutocomplete;