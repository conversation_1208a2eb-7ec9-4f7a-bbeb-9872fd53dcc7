import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Grid,
  Tabs,
  Tab,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Tooltip,
  Divider,
} from "@mui/material";
import {
  CloudUpload,
  Delete,
  Visibility,
  PlayArrow,
  Pause,
  Image as ImageIcon,
  Movie as VideoIcon,
  Gif as GifIcon,
  Person,
  Group,
  Sports,
} from "@mui/icons-material";

// Mock data for previous uploads
const mockUploads = [
  {
    id: 1,
    fileName: "team-photo-2024.jpg",
    fileType: "image/jpeg",
    fileFormat: "Image",
    userType: "Club",
    uploadDate: "2024-01-15",
    fileSize: 2.4,
    isActive: true,
  },
  {
    id: 2,
    fileName: "player-celebration.gif",
    fileType: "image/gif",
    fileFormat: "GIF",
    userType: "Player",
    uploadDate: "2024-01-14",
    fileSize: 5.1,
    isActive: true,
  },
  {
    id: 3,
    fileName: "match-highlights.mp4",
    fileType: "video/mp4",
    fileFormat: "Video",
    userType: "Arbiter",
    uploadDate: "2024-01-13",
    fileSize: 15.7,
    isActive: false,
  },
  {
    id: 4,
    fileName: "referee-decision.jpg",
    fileType: "image/jpeg",
    fileFormat: "Image",
    userType: "Arbiter",
    uploadDate: "2024-01-12",
    fileSize: 1.8,
    isActive: true,
  },
  {
    id: 5,
    fileName: "old-team-photo.png",
    fileType: "image/png",
    fileFormat: "Image",
    userType: "Club",
    uploadDate: "2024-01-10",
    fileSize: 3.2,
    isActive: false,
  },
  {
    id: 6,
    fileName: "player-stats.gif",
    fileType: "image/gif",
    fileFormat: "GIF",
    userType: "Player",
    uploadDate: "2024-01-09",
    fileSize: 4.6,
    isActive: false,
  },
];

function PreviousUploadsDisplay() {
  const [uploads, setUploads] = useState(mockUploads);
  const [tabValue, setTabValue] = useState(0); // 0 = Active, 1 = Inactive
  const [deleteDialog, setDeleteDialog] = useState({ open: false, fileId: null, fileName: "" });

  // Filter uploads based on active status
  const activeUploads = uploads.filter(upload => upload.isActive);
  const inactiveUploads = uploads.filter(upload => !upload.isActive);
  const currentUploads = tabValue === 0 ? activeUploads : inactiveUploads;

  // Get file format icon
  const getFileIcon = (fileFormat) => {
    switch (fileFormat) {
      case "Image":
        return <ImageIcon />;
      case "GIF":
        return <GifIcon />;
      case "Video":
        return <VideoIcon />;
      default:
        return <CloudUpload />;
    }
  };

  // Get user type icon
  const getUserTypeIcon = (userType) => {
    switch (userType) {
      case "Player":
        return <Person />;
      case "Club":
        return <Group />;
      case "Arbiter":
        return <Sports />;
      default:
        return <Person />;
    }
  };

  // Get user type color
  const getUserTypeColor = (userType) => {
    switch (userType) {
      case "Player":
        return "primary";
      case "Club":
        return "secondary";
      case "Arbiter":
        return "success";
      default:
        return "default";
    }
  };

  // Toggle active/inactive status
  const toggleActiveStatus = (fileId) => {
    setUploads(prevUploads =>
      prevUploads.map(upload =>
        upload.id === fileId
          ? { ...upload, isActive: !upload.isActive }
          : upload
      )
    );
  };

  // Handle delete confirmation
  const handleDeleteClick = (fileId, fileName) => {
    setDeleteDialog({ open: true, fileId, fileName });
  };

  // Confirm delete
  const confirmDelete = () => {
    setUploads(prevUploads =>
      prevUploads.filter(upload => upload.id !== deleteDialog.fileId)
    );
    setDeleteDialog({ open: false, fileId: null, fileName: "" });
  };

  // Cancel delete
  const cancelDelete = () => {
    setDeleteDialog({ open: false, fileId: null, fileName: "" });
  };

  // Preview file (mock function)
  const previewFile = (upload) => {
    alert(`Previewing: ${upload.fileName}`);
  };

  // Format file size
  const formatFileSize = (sizeInMB) => {
    return `${sizeInMB} MB`;
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Box maxWidth={1000} mx="auto" p={4}>
      {/* Header */}
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
        Previous Uploads
      </Typography>


    </Box>
  );
}

export default PreviousUploadsDisplay;