import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
  Paper,
  Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import PendingIcon from "@mui/icons-material/Pending";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const RefundTracker = ({ open, onClose, paymentId, transactionId }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [refundData, setRefundData] = useState(null);
  const toast = UseToast();
  const messageId = "6f7edec7-006f-479b-bfe4-b1f444eafe84";

  // Fetch refund status
  useEffect(() => {
    if (!open || !transactionId) return;

    const fetchRefundStatus = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await Client.get(`/payment/refund/status`, {
          params: { 
            transactionId,
            messageId
          }
        });

        if (response.data.success) {
          setRefundData(response.data.data);
        } else {
          setError(response.data.message || "Failed to fetch refund status");
        }
      } catch (error) {
        console.error("Error fetching refund status:", error);
        setError(
          error.response?.data?.message || 
          error.response?.data?.error || 
          "An error occurred while fetching refund status"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchRefundStatus();
  }, [open, transactionId, messageId]);

  // Get step index based on refund status
  const getActiveStep = () => {
    if (!refundData) return 0;

    const status = refundData.status?.toLowerCase();

    if (status === 'refund_failed') {
      return -1; // Special case for failure
    } else if (status === 'refund_pending' || status === 'processing') {
      return 1;
    } else if (status === 'refund_processed' || status === 'refunded') {
      return 2;
    } else {
      return 0;
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "refunded":
      case "refund_processed":
        return "success";
      case "refund_pending":
      case "processing":
        return "warning";
      case "refund_failed":
        return "error";
      default:
        return "default";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case "refunded":
      case "refund_processed":
        return <CheckCircleIcon color="success" />;
      case "refund_pending":
      case "processing":
        return <PendingIcon color="warning" />;
      case "refund_failed":
        return <ErrorIcon color="error" />;
      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3,
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: "#f5f5f5",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography variant="h6" component="div" fontWeight="bold">
          Refund Status Tracker
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : refundData ? (
          <>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                Refund Details
              </Typography>
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  borderRadius: 1,
                  bgcolor: "#f9f9f9",
                }}
              >
                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
                    gap: 2,
                  }}
                >
                  <Typography variant="body2">
                    <strong>Transaction ID:</strong> {refundData.transactionId || "N/A"}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Refund Amount:</strong> {refundData.currency || "INR"}{" "}
                    {refundData.amount || "0.00"}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Refund Type:</strong> {refundData.refundType === "full" ? "Full Refund" : "Partial Refund"}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Requested On:</strong> {formatDate(refundData.createdAt)}
                  </Typography>
                  <Typography variant="body2">
                    <strong>Status:</strong>{" "}
                    <Chip
                      label={refundData.status || "Unknown"}
                      color={getStatusColor(refundData.status)}
                      size="small"
                      icon={getStatusIcon(refundData.status)}
                    />
                  </Typography>
                  {refundData.completedAt && (
                    <Typography variant="body2">
                      <strong>Completed On:</strong> {formatDate(refundData.completedAt)}
                    </Typography>
                  )}
                </Box>

                {refundData.reason && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      <strong>Reason:</strong> {refundData.reason}
                    </Typography>
                  </Box>
                )}

                {refundData.comments && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="body2">
                      <strong>Comments:</strong> {refundData.comments}
                    </Typography>
                  </Box>
                )}
              </Paper>
            </Box>

            <Divider sx={{ my: 3 }} />

            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Refund Progress
            </Typography>

            {getActiveStep() === -1 ? (
              <Alert severity="error" sx={{ mb: 3 }}>
                Your refund request has failed. Please contact support for assistance.
              </Alert>
            ) : (
              <Stepper activeStep={getActiveStep()} alternativeLabel sx={{ mb: 3 }}>
                <Step>
                  <StepLabel>Refund Requested</StepLabel>
                </Step>
                <Step>
                  <StepLabel>Processing</StepLabel>
                </Step>
                <Step>
                  <StepLabel>Completed</StepLabel>
                </Step>
              </Stepper>
            )}

            <Alert 
              severity={
                refundData.status?.toLowerCase() === "refund_failed" ? "error" :
                refundData.status?.toLowerCase() === "refunded" || 
                refundData.status?.toLowerCase() === "refund_processed" ? "success" : "info"
              } 
              sx={{ mt: 2 }}
            >
              {refundData.status?.toLowerCase() === "refund_failed" ? 
                "Your refund request has failed. Please contact support for assistance." :
                refundData.status?.toLowerCase() === "refunded" || 
                refundData.status?.toLowerCase() === "refund_processed" ?
                "Your refund has been processed successfully. The amount will be credited to your original payment method." :
                "Your refund request is being processed. This may take 5-7 business days to complete."}
            </Alert>
          </>
        ) : (
          <Alert severity="info">No refund information available for this transaction.</Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, bgcolor: "#f5f5f5" }}>
        <Button
          onClick={onClose}
          variant="contained"
          color="primary"
          sx={{ fontWeight: "medium" }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RefundTracker;