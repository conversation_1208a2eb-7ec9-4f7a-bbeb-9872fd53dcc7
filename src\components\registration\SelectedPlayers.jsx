import {
  <PERSON>,
  But<PERSON>,
  Container,
  <PERSON>ert,
  AlertTitle,
  CircularProgress,
  Paper,
  Typography,
} from "@mui/material";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import DynamicTable from "../common/DynamicTable";
import BackButton from "../common/BackButton";
import { Client } from "../../api/client";
import { useParams, useSearchParams } from "react-router-dom";
import { useRazorpayBulkPayment } from "../../lib/hooks/useRazorpayBulkPayment";
import UseToast from "../../lib/hooks/UseToast";
import { set } from "react-hook-form";
import Spinner from "../common/Spinner";


const SelectedPlayers = () => {
  const [players, setPlayers] = useState([]);
  const [searchParams] = useSearchParams();
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPayementLoading] = useState(false);

  const { title, id } = useParams();
  const toast = UseToast();

  // Memoize derived values
  const showDetails = useMemo(
    () => searchParams.get("show") === "true",
    [searchParams]
  );

  const {
    initiateBulkPayment,
    isLoading: isPaymentLoading,
    error: paymentError,
    resetError,
    isReady,
    isProcessing,
  } = useRazorpayBulkPayment(toast);

  // Memoize columns to prevent unnecessary re-renders
  const columns = useMemo(
    () => [
      { id: "sno", label: "S.No", format: (value, item, index) => index + 1 },
      { id: "ageCategory", label: "Age Category" },
      { id: "gender", label: "Gender" },
      { id: "playerTitle", label: "Title" },
      { id: "name", label: "Name" },
      { id: "fideId", label: "FIDE ID" },
      { id: "aicfId", label: "National ID" },
      { id: "stateId", label: "State ID" },
      { id: "districtId", label: "District ID" },
    ],
    []
  );

  // Fetch players data with error handling and cleanup
  const fetchRegisteredPlayersList = useCallback(async () => {
    if (!title || !id) return;

    setLoading(true);
    try {
      const response = await Client.get(
        `/tournament/${title}/register/bulk-register/${id}`
      );

      if (response.data.success) {
        setData(response.data.data);
        setPlayers(response.data.data.players || []);
      } else {
        toast.error("Failed to fetch player data");
      }
    } catch (error) {
      console.error("Error fetching players:", error);
      toast.error("Error loading players. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [title, id, toast]);

  useEffect(() => {
    fetchRegisteredPlayersList();
  }, [fetchRegisteredPlayersList]);

  // Handle bulk payment initiation - just trigger payment gateway
  const handleRegister = useCallback(async () => {
    if (showDetails || !data?.bulkRegistrationId) return;
    setPayementLoading(true);

    resetError();
    setLoading(true);

    try {
      // Just initiate payment - success/failure handled by navigation in hook
      await initiateBulkPayment(data.bulkRegistrationId);
    } catch (error) {
      console.error("Payment initiation error:", error);
      toast.error("Failed to start payment process.");
      setPayementLoading(false);
    }
  }, [
    showDetails,
    data?.bulkRegistrationId,
    resetError,
    initiateBulkPayment,
    toast,
  ]);

  // Handle payment retry
  const handleRetry = useCallback(() => {
    resetError();
    handleRegister();
  }, [resetError, handleRegister]);

  // Calculate total amount for display
  const totalAmount = useMemo(() => {
    return data.totalAmount || players.length * (data.entryFee || 0);
  }, [data.totalAmount, data.entryFee, players.length]);

  // Early return for loading state
  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70vh" }}>
        <BackButton />
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            minHeight: "50vh",
          }}
        >
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70vh" }}>
      <BackButton />
      {paymentLoading && (
        <Spinner fullScreen backgroundColor="rgba(255, 255, 255, 0.2)" />
      )}

      {/* Payment Error Display - Only for initiation errors */}
      {paymentError && (
        <Alert severity="error" sx={{ mb: 2, fontSize: "1rem" }}>
          <AlertTitle>Payment Error</AlertTitle>
          {paymentError.message}
          <Button
            onClick={handleRetry}
            disabled={isPaymentLoading || isProcessing}
            size="small"
            sx={{ mt: 1 }}
            variant="outlined"
            color="error"
          >
            Retry Payment
          </Button>
        </Alert>
      )}

      {/* Tournament Info Display */}
      {(data.tournamentTitle || totalAmount > 0) && (
        <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
          {data.tournamentTitle && (
            <Typography variant="h6" gutterBottom>
              {data.tournamentTitle}
            </Typography>
          )}
          <Box sx={{ display: "flex", gap: 3, flexWrap: "wrap" }}>
            <Typography variant="body2" color="text.secondary">
              Players: {players.length}
            </Typography>
            {data.entryFee && (
              <Typography variant="body2" color="text.secondary">
                Entry Fee: ₹{data.entryFee} per player
              </Typography>
            )}
            {totalAmount > 0 && (
              <Typography variant="body1" fontWeight="medium" color="primary">
                Total Amount: ₹{totalAmount}
              </Typography>
            )}
          </Box>
        </Paper>
      )}

      <DynamicTable
        columns={columns}
        data={players}
        loading={false} // We handle loading state above
        page={1}
        totalPages={1}
        onPageChange={() => {}}
        idField="fideId"
        showDetailsButton={false}
      />

      {/* Registration Button - Only visible when not in details view */}
      {!showDetails && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            mt: 3,
            gap: 2,
            flexDirection: "column",
            alignItems: "center",
          }}
        >
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleRegister}
            disabled={
              isPaymentLoading ||
              isProcessing ||
              !isReady ||
              !data?.bulkRegistrationId
            }
            startIcon={
              isPaymentLoading || isProcessing ? (
                <CircularProgress size={20} />
              ) : null
            }
            sx={{
              minWidth: 220,
              height: 48,
              fontSize: "1.1rem",
            }}
          >
            {isPaymentLoading || isProcessing
              ? "Initiating Payment..."
              : "Proceed to Payment"}
          </Button>

          {/* Simple status indicator */}
          {!isReady && !paymentError && (
            <Typography variant="caption" color="text.secondary">
              Loading payment system...
            </Typography>
          )}

          {/* Security Notice */}
          <Box sx={{ textAlign: "center", mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Secure payment powered by Razorpay
            </Typography>
          </Box>
        </Box>
      )}
    </Container>
  );
};

export default SelectedPlayers;
