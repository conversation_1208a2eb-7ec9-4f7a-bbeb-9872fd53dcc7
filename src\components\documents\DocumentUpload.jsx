import React, { useState } from "react";
import {
  Box,
  Button,
  TextField,
  Typography,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
} from "@mui/material";
import { CloudUpload, InsertDriveFile, Close } from "@mui/icons-material";
import compressImage from "../../utils/imageCompressor";

const DocumentUpload = ({ open, onClose, onUpload, isUploading }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileName, setFileName] = useState("");
  const [filePreview, setFilePreview] = useState(null);
  const [error, setError] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    setError(null);

    if (!file) return;

    const isImage = file.type.startsWith("image/");
    const isPdf = file.type === "application/pdf";
    const isDoc =
      file.type === "application/msword" ||
      file.type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

    if (!isImage && !isPdf && !isDoc) {
      setError("Only images (PNG, JPG), PDF, or DOC files are allowed.");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      setError("File must be smaller than 5MB.");
      return;
    }

    setIsProcessing(true);

    try {
      let finalFile = file;

      if (isImage) {
        finalFile = await compressImage(file, 1000);

        const reader = new FileReader();
        reader.onload = (e) => setFilePreview(e.target.result);
        reader.readAsDataURL(finalFile);
      } else {
        setFilePreview(null);
      }

      setSelectedFile(finalFile);
      if (!fileName) {
        const nameWithoutExtension = file.name
          .split(".")
          .slice(0, -1)
          .join(".");
        setFileName(nameWithoutExtension);
      }
    } catch (err) {
      console.error("File processing error:", err);
      setError("Error processing the file.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!selectedFile) {
      setError("Please select a file to upload.");
      return;
    }

    if (!fileName.trim()) {
      setError("Please enter a file name.");
      return;
    }

    onUpload(selectedFile, fileName);

    setSelectedFile(null);
    setFileName("");
    setFilePreview(null);
    onClose();
  };

  const handleDialogClose = () => {
    // Reset form when closing
    setSelectedFile(null);
    setFileName("");
    setFilePreview(null);
    setError(null);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleDialogClose} fullWidth maxWidth="sm">
      <DialogTitle>
        Upload Document
        <IconButton
          onClick={handleDialogClose}
          sx={{ position: "absolute", right: 8, top: 8 }}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <Box component="form" onSubmit={handleSubmit}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            fullWidth
            label="Document Name"
            sx={{ mb: 2, "& .MuiFormLabel-root": { color: "black" } }}
            value={fileName}
            inputProps={{
              maxLength: 50,
            }}
            onChange={(e) => setFileName(e.target.value)}
            required
            disabled={isUploading}
          />

          <Button
            variant="outlined"
            component="label"
            fullWidth
            startIcon={<CloudUpload />}
            sx={{
              mt: 1,
              py: 1.5,
              fontSize: 16,
              display: "flex",
              justifyContent: "flex-start",
            }}
            disabled={isProcessing || isUploading}
          >
            {isProcessing
              ? "Processing..."
              : "Select File (PNG, PDF, DOC - max 10MB)"}
            <input
              type="file"
              hidden
              accept="image/png,image/jpeg,image/jpg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              onChange={handleFileChange}
            />
          </Button>

          {selectedFile && (
            <Box sx={{ mt: 2, display: "flex", alignItems: "center", gap: 2 }}>
              {filePreview ? (
                <Box
                  component="img"
                  src={filePreview}
                  alt="Preview"
                  sx={{
                    width: 100,
                    height: 100,
                    objectFit: "cover",
                    borderRadius: 1,
                    border: "1px solid #ddd",
                  }}
                />
              ) : (
                <InsertDriveFile sx={{ fontSize: 40, color: "primary.main" }} />
              )}
              <Typography>
                {selectedFile.name} ({(selectedFile.size / 1024).toFixed(1)} KB)
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button
          sx={{ fontSize: 16 }}
          onClick={handleDialogClose}
          disabled={isUploading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{ fontSize: 16 }}
          disabled={!selectedFile || isUploading || isProcessing}
        >
          {isUploading ? <CircularProgress size={24} /> : "Upload"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentUpload;
