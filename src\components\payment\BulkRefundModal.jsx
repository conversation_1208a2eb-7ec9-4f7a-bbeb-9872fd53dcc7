import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const BulkRefundModal = ({ 
  open, 
  onClose, 
  bulkRegistrationId,
  tournamentTitle,
  onRefundSuccess 
}) => {
  const [loading, setLoading] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [error, setError] = useState(null);
  const [bulkRegistration, setBulkRegistration] = useState(null);
  const [refundData, setRefundData] = useState({
    reason: "",
    comments: "",
    refundType: "full",
  });
  const [formErrors, setFormErrors] = useState({});
  const toast = UseToast();
  const messageId = "6f7edec7-006f-479b-bfe4-b1f444eafe84";

  // Fetch bulk registration details
  useEffect(() => {
    if (!open || !bulkRegistrationId || !tournamentTitle) return;

    const fetchBulkRegistrationDetails = async () => {
      setLoadingDetails(true);
      setError(null);

      try {
        const response = await Client.get(
          `/tournament/${tournamentTitle}/register/bulk-register/${bulkRegistrationId}`
        );

        if (response.data.success) {
          setBulkRegistration(response.data.data);
        } else {
          setError(response.data.message || "Failed to fetch bulk registration details");
        }
      } catch (error) {
        console.error("Error fetching bulk registration details:", error);
        setError(
          error.response?.data?.message || 
          error.response?.data?.error || 
          "An error occurred while fetching bulk registration details"
        );
      } finally {
        setLoadingDetails(false);
      }
    };

    fetchBulkRegistrationDetails();
  }, [open, bulkRegistrationId, tournamentTitle]);

  // Calculate if the tournament starts in more than 48 hours
  const calculateRefundType = () => {
    if (!bulkRegistration || !bulkRegistration.tournament || !bulkRegistration.tournament.startDate) {
      return "unknown";
    }

    const tournamentStartDate = new Date(bulkRegistration.tournament.startDate);
    const now = new Date();

    // Calculate the difference in hours
    const diffInHours = (tournamentStartDate - now) / (1000 * 60 * 60);

    if (diffInHours < 0) {
      // Tournament has already started
      return "none";
    } else if (diffInHours >= 48) {
      // More than 48 hours before tournament
      return "full";
    } else {
      // Less than 48 hours before tournament
      return "partial";
    }
  };

  const refundEligibility = calculateRefundType();

  const validateForm = () => {
    const errors = {};
    if (!refundData.reason.trim()) {
      errors.reason = "Reason is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setRefundData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for the field being changed
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: null,
      }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    if (refundEligibility === "none") {
      setError("Refunds are not available after the tournament has started.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await Client.post(`/payment/bulk-refund`, {
        bulkRegistrationId: bulkRegistrationId,
        tournamentId: bulkRegistration.tournament.id,
        reason: refundData.reason,
        comments: refundData.comments,
        refundType: refundData.refundType,
        messageId: messageId,
      });

      if (response.data.success) {
        toast.success("Bulk refund initiated successfully");

        if (onRefundSuccess) {
          onRefundSuccess(response.data.data);
        }
        onClose();
      } else {
        setError(response.data.message || "Failed to initiate bulk refund");
      }
    } catch (error) {
      console.error("Bulk refund error:", error);
      setError(
        error.response?.data?.message || 
        error.response?.data?.error || 
        "An error occurred while processing the bulk refund request"
      );
    } finally {
      setLoading(false);
    }
  };

  // Get refund message based on refund eligibility
  const getRefundMessage = () => {
    switch (refundEligibility) {
      case "full":
        return "All players will receive a full refund.";
      case "partial":
        return "All players will receive a partial refund as it's less than 48 hours before the tournament.";
      case "none":
        return "No refunds are available as the tournament has already started.";
      default:
        return "Refund eligibility will be determined based on the tournament's start date.";
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3,
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: "#f5f5f5",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography variant="h6" component="div" fontWeight="bold">
          Bulk Registration Refund
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          disabled={loading}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loadingDetails ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : bulkRegistration ? (
          <>
            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                Bulk Registration Details
              </Typography>
              <Box
                sx={{
                  display: "grid",
                  gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
                  gap: 2,
                  bgcolor: "#f9f9f9",
                  p: 2,
                  borderRadius: 1,
                }}
              >
                <Typography variant="body2">
                  <strong>Registration ID:</strong> {bulkRegistration.bulkRegistrationId || "N/A"}
                </Typography>
                <Typography variant="body2">
                  <strong>Tournament:</strong> {bulkRegistration.tournament?.title?.replace(/-/g, " ") || "N/A"}
                </Typography>
                <Typography variant="body2">
                  <strong>Total Players:</strong> {bulkRegistration.players?.length || 0}
                </Typography>
                <Typography variant="body2">
                  <strong>Total Amount:</strong> {bulkRegistration.tournament?.entryFeeCurrency || "INR"}{" "}
                  {(bulkRegistration.tournament?.entryFee * bulkRegistration.players?.length).toFixed(2) || "0.00"}
                </Typography>
                <Typography variant="body2">
                  <strong>Registration Date:</strong> {bulkRegistration.createdAt 
                    ? new Date(bulkRegistration.createdAt).toLocaleDateString() 
                    : "N/A"}
                </Typography>
                <Typography variant="body2">
                  <strong>Tournament Start:</strong> {bulkRegistration.tournament?.startDate 
                    ? new Date(bulkRegistration.tournament.startDate).toLocaleDateString() 
                    : "N/A"}
                </Typography>
              </Box>
            </Box>

            <Alert 
              severity={
                refundEligibility === "full" ? "success" : 
                refundEligibility === "partial" ? "warning" : 
                "error"
              } 
              sx={{ mb: 3 }}
            >
              {getRefundMessage()}
            </Alert>

            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Registered Players ({bulkRegistration.players?.length || 0})
            </Typography>

            <Box sx={{ maxHeight: '200px', overflow: 'auto', mb: 3, border: '1px solid #eee', borderRadius: 1 }}>
              <List dense>
                {bulkRegistration.players?.map((player, index) => (
                  <ListItem key={index} divider={index < bulkRegistration.players.length - 1}>
                    <ListItemText 
                      primary={player.playerName || `Player ${index + 1}`} 
                      secondary={`Age: ${player.ageCategory || 'N/A'}, Gender: ${player.genderCategory || 'N/A'}`}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>

            <Divider sx={{ my: 2 }} />

            <FormControl fullWidth margin="normal" error={!!formErrors.reason}>
              <InputLabel id="reason-label">Reason for Refund</InputLabel>
              <Select
                labelId="reason-label"
                id="reason"
                name="reason"
                value={refundData.reason}
                onChange={handleChange}
                label="Reason for Refund"
                disabled={loading || refundEligibility === "none"}
              >
                <MenuItem value="player_cancelled">Players Cannot Attend</MenuItem>
                <MenuItem value="tournament_changes">Tournament Changes</MenuItem>
                <MenuItem value="scheduling_conflict">Scheduling Conflict</MenuItem>
                <MenuItem value="duplicate_payment">Duplicate Payment</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </Select>
              {formErrors.reason && (
                <FormHelperText error>{formErrors.reason}</FormHelperText>
              )}
            </FormControl>

            <FormControl fullWidth margin="normal" error={!!formErrors.refundType}>
              <InputLabel id="refund-type-label">Refund Type</InputLabel>
              <Select
                labelId="refund-type-label"
                id="refundType"
                name="refundType"
                value={refundData.refundType}
                onChange={handleChange}
                label="Refund Type"
                disabled={loading || refundEligibility === "none"}
              >
                <MenuItem value="full">Full Refund</MenuItem>
                <MenuItem value="partial" disabled={refundEligibility === "full"}>Partial Refund</MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              margin="normal"
              label="Additional Comments"
              name="comments"
              value={refundData.comments}
              onChange={handleChange}
              multiline
              rows={3}
              disabled={loading || refundEligibility === "none"}
            />
          </>
        ) : (
          <Alert severity="info">No bulk registration information available.</Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, bgcolor: "#f5f5f5" }}>
        <Button
          onClick={onClose}
          color="inherit"
          disabled={loading}
          sx={{ fontWeight: "medium" }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={loading || loadingDetails || !bulkRegistration || refundEligibility === "none"}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
          sx={{ fontWeight: "medium" }}
        >
          {loading ? "Processing..." : "Process Bulk Refund"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkRefundModal;