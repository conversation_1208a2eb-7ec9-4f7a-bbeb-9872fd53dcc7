import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  IconButton,
  Divider,
  Button,
  Skeleton,
  Paper,
} from "@mui/material";
import {
  Notifications as NotificationsIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
} from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import EmptyState from "../common/EmptyState";

const GeneralNotificationsTab = ({ loading: initialLoading }) => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(initialLoading);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const toast = UseToast();

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "";
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 60) {
      return `${diffMins} minute${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric' 
      });
    }
  };

  // Fetch notifications
  const fetchNotifications = useCallback(async (pageNum = 1, append = false) => {
    setLoading(true);
    try {
      const response = await Client.get("/notifications", {
        params: { page: pageNum, limit: 10, type: "general" },
      });
      
      if (response.data.success) {
        const newNotifications = response.data.data.notifications || [];
        setNotifications(prev => append ? [...prev, ...newNotifications] : newNotifications);
        setHasMore(newNotifications.length === 10);
      } else {
        toast.error(response.data.message || "Failed to fetch notifications");
      }
    } catch (error) {
      console.error("Error fetching notifications:", error);
      toast.error("An error occurred while fetching notifications");
    } finally {
      setLoading(false);
    }
  }, []);

  // Load more notifications
  const loadMore = () => {
    if (hasMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNotifications(nextPage, true);
    }
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      const response = await Client.put(`/notifications/${notificationId}/read`);
      if (response.data.success) {
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === notificationId 
              ? { ...notification, read: true } 
              : notification
          )
        );
      } else {
        toast.error(response.data.message || "Failed to mark notification as read");
      }
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("An error occurred while updating notification");
    }
  };

  // Delete notification
  const deleteNotification = async (notificationId) => {
    try {
      const response = await Client.delete(`/notifications/${notificationId}`);
      if (response.data.success) {
        setNotifications(prev => prev.filter(notification => notification.id !== notificationId));
        toast.success("Notification deleted successfully");
      } else {
        toast.error(response.data.message || "Failed to delete notification");
      }
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error("An error occurred while deleting notification");
    }
  };

  // Mark all as read
  const markAllAsRead = async () => {
    try {
      const response = await Client.put("/notifications/read-all", { type: "general" });
      if (response.data.success) {
        setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
        toast.success("All notifications marked as read");
      } else {
        toast.error(response.data.message || "Failed to mark all notifications as read");
      }
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("An error occurred while updating notifications");
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, []);

  // Loading skeleton
  if (loading && notifications.length === 0) {
    return (
      <Box>
        {Array(5).fill(0).map((_, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Skeleton variant="rectangular" height={80} />
          </Box>
        ))}
      </Box>
    );
  }

  // Empty state
  if (notifications.length === 0 && !loading) {
    return (
      <EmptyState
        icon={<NotificationsIcon sx={{ fontSize: 60, color: "text.secondary" }} />}
        title="No Notifications"
        description="You don't have any notifications at the moment."
      />
    );
  }

  return (
    <Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
        <Button 
          variant="outlined" 
          onClick={markAllAsRead}
          startIcon={<CheckCircleIcon />}
          disabled={notifications.every(n => n.read)}
        >
          Mark All as Read
        </Button>
      </Box>

      <Paper elevation={0} variant="outlined">
        <List sx={{ width: "100%" }}>
          {notifications.map((notification, index) => (
            <React.Fragment key={notification.id || index}>
              <ListItem
                alignItems="flex-start"
                secondaryAction={
                  <Box>
                    {!notification.read && (
                      <IconButton 
                        edge="end" 
                        aria-label="mark as read"
                        onClick={() => markAsRead(notification.id)}
                        sx={{ mr: 1 }}
                      >
                        <CheckCircleIcon />
                      </IconButton>
                    )}
                    <IconButton 
                      edge="end" 
                      aria-label="delete"
                      onClick={() => deleteNotification(notification.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                }
                sx={{
                  bgcolor: notification.read ? "transparent" : "rgba(25, 118, 210, 0.08)",
                  "&:hover": { bgcolor: "rgba(0, 0, 0, 0.04)" },
                }}
              >
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: notification.read ? "grey.400" : "primary.main" }}>
                    <NotificationsIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography
                      variant="subtitle1"
                      fontWeight={notification.read ? "normal" : "bold"}
                    >
                      {notification.title}
                    </Typography>
                  }
                  secondary={
                    <React.Fragment>
                      <Typography
                        variant="body2"
                        color="text.primary"
                        component="span"
                        display="block"
                        sx={{ mb: 1 }}
                      >
                        {notification.message}
                      </Typography>
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        component="span"
                      >
                        {formatDate(notification.createdAt)}
                      </Typography>
                    </React.Fragment>
                  }
                />
              </ListItem>
              {index < notifications.length - 1 && <Divider component="li" />}
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {hasMore && (
        <Box sx={{ textAlign: "center", mt: 2 }}>
          <Button 
            variant="outlined" 
            onClick={loadMore}
            disabled={loading}
          >
            {loading ? "Loading..." : "Load More"}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default GeneralNotificationsTab;
