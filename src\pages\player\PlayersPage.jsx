import React, { useState } from "react";
import { <PERSON>, Button, Container } from "@mui/material";
import { Client } from "../../api/client";

import { Link } from "react-router-dom";
import DynamicTable from "../../components/common/DynamicTable";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { ArrowBack } from "@mui/icons-material";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";
import PlayersSearchForm from "../../components/common/PlayersSearchForm";
import BackButton from "../../components/common/BackButton";

const PlayersPage = () => {
  // States for form inputs
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",
    country: "",
    state: "",
    district: "",
    city: "",
  });

  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 10;
  const [players, setPlayers] = useState([]);
  const toast = UseToast();

  // Define fetchPlayers function
  const fetchPlayers = async (currentPage = page) => {
    setLoading(true);

    try {
      const response = await Client.get("/player", {
        params: { ...search, page: currentPage, limit },
      });

      if (response.status === 204) {
        setPlayers([]);
        toast.info("No players found");
        return;
      }

      const { players, currentPage: cp, totalPages: tp } = response.data.data;
      setTotalPages(tp || 1);
      setPage(cp || 1);
      setPlayers(players || []);
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 422) {
        console.error("Validation error:", error.response.data);
        toast.info("Please correct the search criteria");
      } else {
        console.error("Error fetching players:", error);
        toast.error("Failed to fetch players. Please try again.");
      }
      setPlayers([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search button click
  const handleSearch = (pageNum = 1) => {
    setPage(pageNum);
    fetchPlayers(pageNum);
  };

  const handleReset = () => {
    setSearch({
      playerName: "",
      playerId: "",
      country: "",
      state: "",
      district: "",
      city: "",
    });
    setPage(1);
    // Don't trigger search here, let user click search manually after reset
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchPlayers(newPage);
  };



  return (
    <Container maxWidth="xl" sx={{ pt: 2, pb: 8 }}>
      {/* Back Button */}
      <BackButton />

      {/* Search Form */}
      <PlayersSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={() => handleSearch(1)}
        handleReset={handleReset}
        loading={loading}
        mobileEmail={false}
      />

      {/* Players Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          { id: "playerTitle", label: "Title", width: "80px" },
          {
            id: "name",
            label: "Player Name",
            format: (_, item) => (
              <Link to={`/players/${item.cbid}`}>{item.name}</Link>
            ),
          },
          { id: "fideId", label: "FIDE ID" },
          { id: "aicfId", label: "AICF ID" },
          { id: "stateId", label: "State ID" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
        ]}
        data={players}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/players/"
        idField="cbid"
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default PlayersPage;
