import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Chip,
  MenuItem,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import { RestartAlt } from "@mui/icons-material";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const MyCertificatesPage = () => {
  const [certificates, setCertificates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);

  // States for form inputs
  const [search, setSearch] = useState({
    tournamentTitle: "",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  // Handler functions
  const handlePageChange = (_, value) => {
    setPage(value);
    fetchCertificates(value);
  };

  const handleReset = () => {
    setSearch({
      tournamentTitle: "",
    });
    fetchCertificates(1);
  };

  const handleSearch = () => {
    fetchCertificates(1);
  };

  const fetchCertificates = useCallback(
    async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
        };

        // Only add non-empty search parameters
        Object.keys(search).forEach((key) => {
          if (search[key] !== "") {
            params[key] = search[key];
          }
        });

        const response = await Client.get("/player/documents/certificates", {
          params,
        });

        if (response.status === 204) {
          toast.info("No certificate records found");
          setCertificates([]);
          setTotalRecords(0);
          setTotalPages(0);
          setPage(1);
          return;
        }

        if (!response.data.success) {
          toast.info("No certificate records found");
          return;
        }

        setCertificates(response.data.data.certificates);
        setTotalRecords(response.data.data.total);
        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching certificate history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
    },
    [limit, search]
  );

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Initial fetch of certificates
  useEffect(() => {
    fetchCertificates(1);
  }, []);

  const handleDownloadCertificate = async (certificateData) => {
    try {
      const { generateCertificateAndDownload } = await import(
        "../../utils/generateCertificateAndDownload.jsx"
      );

      await generateCertificateAndDownload({ toast, certificateData });
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Certificate generation failed:", error);
      toast.error("Failed to generate certificate PDF");
    }
  };
  return (
    <Container
      maxWidth="xl"
      sx={{
        pt: 2,

        pb: 8,
        minHeight: "70dvh",
      }}
    >
      <BackButton />
      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          My Certificates
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.tournamentTitle}
              onChange={(e) =>
                setSearch({ ...search, tournamentTitle: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter tournament title"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3} sx={{ display: "flex", gap: 1 }}>
            <Button
              variant="containedSecondary"
              color="secondary"
              sx={{
                width: "40px",
              }}
              onClick={handleReset}
              disabled={loading}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* certificate History Table */}
      <DynamicTable
        columns={[
          {
            id: "certificateId",
            label: "Certificate ID",
            format: (_, certificate) => certificate.certificateId || "N/A",
          },
          {
            id: "tournamentTitle",
            label: "Tournament Name",
            format: (_, certificate) => certificate.tournamentTitle || "N/A",
          },
          {
            id: "date",
            label: "Date",
            format: (_, certificate) => formatDate(certificate.tournamentDate),
          },

          {
            id: "Download",
            label: "Download",
            format: (_, certificate) =>
              certificate.id ? (
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => handleDownloadCertificate(certificate)}
                >
                  Download Certificate
                </Button>
              ) : (
                "N/A"
              ),
          },
        ]}
        data={certificates}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath=""
        idField="id"
        showDetailsButton={false}
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />

      {/* Record Count */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 2,
          p: 2,
          bgcolor: "#fafafa",
          borderRadius: 1,
        }}
      >
        <Typography variant="h6" color="text.primary">
          Showing {certificates?.length} of {totalRecords} records
        </Typography>
      </Box>
    </Container>
  );
};

export default MyCertificatesPage;
