import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Container,
  Paper,
  Tabs,
  Tab,
  Typography,
  Badge,
  useTheme,
  useMediaQuery,
} from "@mui/material";
import {
  People as PeopleIcon,
  Business as BusinessIcon,
} from "@mui/icons-material";

import ClubInvitesTab from "../../components/notifications/ClubInvitesTab";
import FriendRequestsTab from "../../components/notifications/FriendRequestsTab";
import BackButton from "../../components/common/BackButton";

// Tab panel component to handle tab content
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notifications-tabpanel-${index}`}
      aria-labelledby={`notifications-tab-${index}`}
      {...other}
      style={{ width: "100%" }}
    >
      {value === index && <Box sx={{ p: { xs: 2, md: 3 } }}>{children}</Box>}
    </div>
  );
}

const NotificationsPage = () => {
  const [activeTab, setActiveTab] = useState(0);
  const location = useLocation();
  const navigate = useNavigate();

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // Update active tab based on route
  useEffect(() => {
    const path = location.pathname;
    if (path.includes("friend-requests")) {
      setActiveTab(1);
    } else {
      setActiveTab(0); // default to club invites
    }
  }, [location.pathname]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    const paths = [
      "/dashboard/notifications/club-invites",
      "/dashboard/notifications/friend-requests",
    ];
    navigate(paths[newValue]);
  };

  return (
    <Container maxWidth="xl" sx={{ py: { xs: 2, md: 4 } }}>
      {/* Back Button and Heading */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          mb: 3,
          gap: 2,
        }}
      >
        <BackButton to="/dashboard" />
      </Box>

      {/* Notifications Panel */}
      <Paper
        elevation={2}
        sx={{
          borderRadius: 3,
          overflow: "hidden",
          transition: "all 0.3s ease",
          bgcolor: theme.palette.background.paper,
          "&:hover": {
            boxShadow: theme.shadows[4],
          },
        }}
      >
        <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
          {/* Tabs */}
          <Box
            sx={{
              bgcolor: theme.palette.mode === "light" ? "grey.50" : "grey.900",
              width: { sm: "250px", md: "100%" },
            }}
          >
            <Tabs
              orientation="horizontal"
              variant="scrollable"
              value={activeTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              sx={{
                "& .MuiTab-root": {
                  alignItems: isMobile ? "center" : "flex-start",
                  justifyContent: isMobile ? "center" : "flex-start",
                  textAlign: "left",
                  py: 2.5,
                  px: { xs: 2, md: 3 },
                  minHeight: 64,
                  transition: "all 0.2s ease",
                  "&.Mui-selected": {
                    bgcolor:
                      theme.palette.mode === "light"
                        ? "rgba(25, 118, 210, 0.08)"
                        : "rgba(144, 202, 249, 0.08)",
                  },
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "light"
                        ? "rgba(0, 0, 0, 0.04)"
                        : "rgba(255, 255, 255, 0.04)",
                  },
                },
              }}
            >
              <Tab
                icon={
                  <Badge color="error" max={99}>
                    <BusinessIcon sx={{ color: "text.primary" }} />
                  </Badge>
                }
                iconPosition="start"
                label={
                  <Typography sx={{ ml: { md: 1 }, color: "text.primary" }}>
                    Club Invites
                  </Typography>
                }
                sx={{ textTransform: "none" }}
              />
              <Tab
                icon={
                  <Badge color="error" max={99}>
                    <PeopleIcon sx={{ color: "text.primary" }} />
                  </Badge>
                }
                iconPosition="start"
                label={
                  <Typography sx={{ ml: { md: 1 }, color: "text.primary" }}>
                    Friend Requests
                  </Typography>
                }
                sx={{ textTransform: "none" }}
              />
            </Tabs>
          </Box>

          {/* Tab Content */}
          <Box sx={{ flexGrow: 1, height: "100%" }}>
            <TabPanel value={activeTab} index={0}>
              <ClubInvitesTab />
            </TabPanel>
            <TabPanel value={activeTab} index={1}>
              <FriendRequestsTab />
            </TabPanel>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default NotificationsPage;
