import React from "react";
import { Typography, TextField as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Fade } from "@mui/material";

const TextField = React.memo(
  ({
    value = "",
    onChange,
    name,
    placeholder,
    error,
    validateField,
    title,
    required,
  }) => {
    return (
      <>
        <Typography
          variant="h6"
          sx={{
            textAlign: "start",
            p: "0px !important",
            m: "0px !important",
          }}
        >
          {title}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
        <MuiTextField
          fullWidth
          name={name}
          variant="outlined"
          margin="normal"
          placeholder={placeholder}
          value={value}
          sx={{ minHeight: 70 }}
          onChange={(e) => {
            const value = e.target.value;
            onChange(name, value);
          }}
          onBlur={() => {
            validateField(name, value);
          }}
          error={!!error}
          helperText={
            <Fade in={!!error}>
              <Typography component="span" variant="caption" color="error">
                {error}
              </Typography>
            </Fade>
          }
        />
      </>
    );
  }
);

export default TextField;
