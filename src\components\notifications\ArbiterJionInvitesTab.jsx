import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  Skeleton,
  Paper,
  Divider,
  Chip,
  CircularProgress,
  Badge,
  IconButton,
  Toolt<PERSON>,
  Rating,
} from "@mui/material";
import {
  SportsEsports as ChessIcon,
  EmojiEvents as TrophyIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Notifications as NotificationsIcon,
  Refresh as RefreshIcon,
  AccessTime as TimeIcon,
  Pending,
} from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import EmptyState from "../common/EmptyState";
import { Link, useNavigate } from "react-router-dom";

const ArbiterJionInvitesTab = ({ loading: initialLoading }) => {
  const [invites, setInvites] = useState([]);
  const [loading, setLoading] = useState(initialLoading);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const toast = UseToast();
  const Navigate = useNavigate();

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "";

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // Format the full date for display in tooltip
    const fullDateFormatted = date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

    // Format for relative time display
    let relativeTime;
    if (diffMins < 60) {
      relativeTime = `${diffMins} minute${diffMins !== 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      relativeTime = `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    } else if (diffDays < 7) {
      relativeTime = `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    } else {
      relativeTime = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    }

    return { relativeTime, fullDateFormatted };
  };

  // Remove unused function

  const fetchInvites = useCallback(async (pageNum = 1) => {
    setLoading(true);

    try {
      const response = await Client.get("/arbiter/profile/tournament-invite", {
        params: { page: pageNum, limit: 10 },
      });

      if (response.data.success) {
        const newInvites = response.data.data || [];
        setInvites(newInvites);
        setHasMore(newInvites.length === 10);
      } else {
        toast.error(response.data.message || "Failed to fetch  invites");
      }
    } catch (error) {
      console.error("Error fetching invites:", error);
      toast.error("An error occurred while fetching  invites");
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh invites
  const refreshInvites = async () => {
    setRefreshing(true);
    setPage(1);
    await fetchInvites(1, false);
    setRefreshing(false);
  };

  // Load more invites
  const loadMore = () => {
    if (hasMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchInvites(nextPage, true);
    }
  };

  // Accept club invite
  const handleInviteAction = async (action, tournamentId) => {
    try {
      const response = await Client.post(`/arbiter/profile/tournament-invite`, {
        action,
        tournamentId,
      });
      if (response.data.success) {
        toast.success(`${action} successfully`);
        refreshInvites();
      } else {
        toast.info("Failed to accept chess club invitation");
      }
    } catch (error) {
      console.error("Error accepting chess club invitation:", error);
      if (error.response.status === 404) {
        toast.info(error.response.data.error.message);
      }
      if (error.response.status === 409) {
        toast.error(error.response.data.error.message);
      }
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchInvites();
  }, [fetchInvites]);

  // Get unread invites count
  const unreadInvitesCount = invites.length;

  // Loading skeleton
  if (loading && invites.length === 0) {
    return (
      <Box>
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
                  {/* <Skeleton
                    variant="circular"
                    width={56}
                    height={56}
                    sx={{ mr: 2 }}
                  /> */}
                  <Box sx={{ width: "100%" }}>
                    <Skeleton variant="text" height={15} width="40%" />
                    <Skeleton variant="text" height={20} width="70%" />
                  </Box>
                </Box>
              </Paper>
            </Box>
          ))}
      </Box>
    );
  }

  // Empty state
  if (invites.length === 0 && !loading) {
    return (
      <EmptyState
        icon={<ChessIcon sx={{ fontSize: 60, color: "black" }} />}
        title="No Chess Tournament Invitations"
        description="You don't have any chess Tournament invitations at the moment."
        action={
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={refreshInvites}
            disabled={refreshing}
            sx={{ fontSize: 16 }}
          >
            Refresh
          </Button>
        }
      />
    );
  }

  return (
    <Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <ChessIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6">
            Chess Tournament Invitations
            {unreadInvitesCount > 0 && (
              <Badge
                badgeContent={unreadInvitesCount}
                color="error"
                sx={{ ml: 2 }}
              />
            )}
          </Typography>
        </Box>
        <Tooltip title="Refresh invitations">
          <IconButton
            onClick={refreshInvites}
            disabled={refreshing}
            size="small"
          >
            {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      <Paper
        elevation={0}
        variant="outlined"
        sx={{ borderRadius: 1, overflow: "hidden" }}
      >
        <List sx={{ width: "100%", p: 0 }}>
          {invites.map((invite, index) => {
            const tournament = invite.metadata?.tournament || {};

            return (
              <React.Fragment key={invite.id || index}>
                <ListItem
                  alignItems="flex-start"
                  sx={{
                    "&:hover": { bgcolor: "rgba(0, 0, 0, 0.04)" },
                    py: 2,
                    px: { xs: 1, sm: 2 },
                    transition: "background-color 0.2s ease",
                    bgcolor: !invite.isRead
                      ? "rgba(25, 118, 210, 0.05)"
                      : "transparent",
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      src={""}
                      alt={""}
                      sx={{
                        width: 56,
                        height: 56,
                        mr: 2,
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                      }}
                    >
                      <ChessIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: { xs: "column", sm: "row" },
                          alignItems: { xs: "flex-start", sm: "center" },
                          mb: 0.5,
                        }}
                      >
                        <Typography
                          variant="subtitle1"
                          fontWeight="medium"
                          component={Link}
                          to={`/tournaments/${tournament.tournamentName}`}
                          sx={{
                            textDecoration: "none",
                            color: "primary.main",
                            "&:hover": { textDecoration: "underline" },
                          }}
                        >
                          {invite.title || "Chess tournament Invitation"}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <React.Fragment>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 0.5,
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="text.primary"
                            component="span"
                            title={
                              formatDate(invite.createdAt).fullDateFormatted
                            }
                            sx={{
                              cursor: "help",
                              borderBottom: "1px dotted",
                              borderColor: "text.disabled",
                            }}
                          >
                            {formatDate(invite.createdAt).relativeTime}
                          </Typography>
                          <Chip
                            label="New"
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{
                              height: 18,
                              fontSize: "0.6rem",
                              ml: 1,
                              display: invite.isRead ? "none" : "inline-flex",
                            }}
                          />
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: "row",
                            gap: 1,
                            alignItems: "center",
                            justifyContent: "space-between",
                          }}
                        >
                          <Typography
                            variant="body2"
                            color="text.primary"
                            component="span"
                            display="flex"
                            sx={{
                              mb: 1,
                              mt: 0.5,
                              textAlign: "start",
                            }}
                          >
                            {"Invited you to join their chess tournament"}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              flexDirection: { xs: "column", sm: "row" },
                              alignItems: { xs: "flex-start", sm: "center" },
                            }}
                          >
                            <Box
                              sx={{
                                mt: { xs: 1, sm: 0 },
                                display: "flex",
                                gap: 1,
                              }}
                            >
                              <Button
                                variant="contained"
                                size="small"
                                startIcon={<CheckIcon />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleInviteAction(
                                    "accept",
                                    tournament.tournamentId
                                  );
                                }}
                                color="success"
                                disableElevation
                              >
                                Accept
                              </Button>
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<CloseIcon />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleInviteAction(
                                    "reject",
                                    tournament.tournamentId
                                  );
                                }}
                                color="error"
                              >
                                Reject
                              </Button>
                            </Box>
                          </Box>
                        </Box>
                      </React.Fragment>
                    }
                  />
                </ListItem>
                {index < invites.length - 1 && <Divider component="li" />}
              </React.Fragment>
            );
          })}
        </List>
      </Paper>

      {hasMore && (
        <Box sx={{ textAlign: "center", mt: 2 }}>
          <Button
            variant="outlined"
            onClick={loadMore}
            disabled={loading}
            startIcon={loading && <CircularProgress size={16} />}
          >
            {loading ? "Loading..." : "Load More"}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default ArbiterJionInvitesTab;
