import React, { useEffect, useState } from "react";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Box,
  Button,
  Grid,
  Typography,
  Paper,
  Divider,
  Alert,
  Skeleton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Card,
  CardContent,
} from "@mui/material";
import {
  AccountBalance as BankIcon,
  Edit as EditIcon,
  Verified as VerifiedIcon,
  Pending as PendingIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
} from "@mui/icons-material";
import Spinner from "../../components/common/Spinner";
import FormTextField from "../../components/form/FormTextField";
import FormNumberField from "../../components/form/FormNumberField";
import FormSelectField from "../../components/form/FormSelectField";
import BankDetailsUpdateRequest from "../../components/form/BankDetailsUpdateRequest";
import BackButton from "../../components/common/BackButton";

const bankDetailsSchema = z.object({
  bankName: z
    .string()
    .min(2, "Bank Name is required")
    .max(100, "Bank Name is too long"),
  AccountNumber: z
    .string()
    .min(9, "Account Number must be at least 9 digits")
    .max(18, "Account Number must not exceed 18 digits")
    .regex(/^\d+$/, "Account Number must contain only digits"),
  branchIFSCCode: z
    .string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, "Invalid IFSC code")
    .length(11, "IFSC code must be exactly 11 characters"),
  branchName: z
    .string()
    .min(2, "Branch Name is required")
    .max(100, "Branch Name is too long"),
  bankAccountType: z
    .string()
    .min(2, "Bank Account Type is required")
    .max(50, "Bank Account Type is too long"),
  bankAccountHolderName: z
    .string()
    .min(2, "Bank Account Holder Name is required")
    .max(100, "Bank Account Holder Name is too long"),
});

// Skeleton component for bank details table
const BankDetailsTableSkeleton = () => {
  return (
    <Card elevation={3} sx={{ mb: 4 }}>
      <CardContent sx={{ p: 3 }}>
        <Skeleton variant="text" width="40%" height={60} sx={{ mb: 3 }} />
        
        <TableContainer>
          <Table>
            <TableBody>
              {[...Array(6)].map((_, index) => (
                <TableRow key={index}>
                  <TableCell sx={{ width: '30%', py: 2 }}>
                    <Skeleton variant="text" width="80%" height={24} />
                  </TableCell>
                  <TableCell sx={{ py: 2 }}>
                    <Skeleton variant="text" width="60%" height={24} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Divider sx={{ my: 3 }} />
        
        <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
          <Skeleton variant="rectangular" width={120} height={40} sx={{ borderRadius: 1 }} />
          <Skeleton variant="rectangular" width={150} height={40} sx={{ borderRadius: 1 }} />
        </Box>
      </CardContent>
    </Card>
  );
};

// Skeleton component for bank details form
const BankingFormSkeleton = () => {
  return (
    <Card elevation={3}>
      <CardContent sx={{ p: 3 }}>
        <Skeleton variant="text" width="40%" height={60} sx={{ mb: 4 }} />

        <Grid container spacing={3}>
          {[...Array(6)].map((_, index) => (
            <Grid item xs={12} md={6} key={index}>
              <Skeleton variant="text" width="60%" height={30} sx={{ mb: 1 }} />
              <Skeleton
                variant="rectangular"
                width="100%"
                height={56}
                sx={{ borderRadius: 1 }}
              />
            </Grid>
          ))}
        </Grid>

        <Box sx={{ display: "flex", justifyContent: "center", width: "100%", mt: 4 }}>
          <Skeleton variant="rectangular" width={120} height={48} sx={{ borderRadius: 1 }} />
        </Box>
      </CardContent>
    </Card>
  );
};

const ClubBankingDetails = () => {
  const [bankDetails, setBankDetails] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [updateRequestOpen, setUpdateRequestOpen] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const toast = UseToast();

  const fetchBankDetails = async () => {
    try {
      setLoading(true);
      const response = await Client.get("/club/profile/bankdetails");
      
      // Handle 404 - no bank details found
      if (response.status === 404) {
        setShowCreateForm(true);
        toast.info("You haven't added your bank details yet. Please create them.");
        return;
      }
      
      if (!response.data.success) {
        toast.error(response.data.error || "Failed to fetch bank details");
        return;
      }
      
      const bankData = response.data.data;
      setBankDetails(bankData);
      setShowCreateForm(false);
      
    } catch (error) {
      console.error("Error fetching bank details:", error);
      
      if (error.response?.status === 404) {
        setShowCreateForm(true);
        toast.info("You haven't added your bank details yet. Please create them.");
      } else {
        const errorMessage = error.response?.data?.error || "Failed to fetch bank details";
        toast.error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDetails = async () => {
    try {
      setVerifying(true);
      toast.info("Verifying bank details...");

      const response = await Client.post("/club/profile/bankdetails/verify-status");

      if (!response.data.success) {
        toast.error(response.data.error || "Verification failed");
        return;
      }

      toast.success("Bank details verified successfully!");

      // Show verification details if available
      const verificationDetails = response.data.data.verification_details;
      if (verificationDetails?.amount_deposited) {
        toast.info(`Verification deposit: ${verificationDetails.amount_deposited}`);
      }

      // Refresh bank details
      await fetchBankDetails();

    } catch (error) {
      console.error("Error verifying bank details:", error);
      const errorMessage = error.response?.data?.error || "Verification failed";
      toast.error(errorMessage);
    } finally {
      setVerifying(false);
    }
  };

  useEffect(() => {
    fetchBankDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleUpdateRequest = () => {
    setUpdateRequestOpen(true);
  };

  const handleCloseUpdateRequest = () => {
    setUpdateRequestOpen(false);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    fetchBankDetails();
  };

  return (
    <Box sx={{ maxWidth: "xl", margin: "auto", mb:2, p: 4 }}>
      <BackButton />
      
      {loading && !showCreateForm && <BankDetailsTableSkeleton />}
      {loading && showCreateForm && <BankingFormSkeleton />}

      {/* Bank Details Table View */}
      {!loading && !showCreateForm && bankDetails && (
        <Card elevation={3} sx={{ mb: 4 }}>
          <CardContent sx={{ p: 3 }}>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <BankIcon sx={{ fontSize: 40, color: "primary.main" }} />
                <Typography variant="h4">Bank Details</Typography>
              </Box>
              <Box sx={{ display: "flex", gap: 1 }}>
                <Chip
                  icon={bankDetails.isVerified ? <VerifiedIcon /> : <PendingIcon />}
                  label={bankDetails.isVerified ? "Verified" : "Pending Verification"}
                  color={bankDetails.isVerified ? "success" : "warning"}
                  variant={bankDetails.isVerified ? "filled" : "outlined"}
                />
                <Chip
                  icon={bankDetails.isLocked ? <LockIcon /> : <UnlockIcon />}
                  label={bankDetails.isLocked ? "Locked" : "Unlocked"}
                  color={bankDetails.isLocked ? "error" : "success"}
                  variant="outlined"
                />
              </Box>
            </Box>

            {/* Status Alert */}
            {bankDetails.isLocked ? (
              <Alert severity="info" sx={{ mb: 3, fontSize: 16 }}>
                <strong>Bank details are locked.</strong> Any changes require admin approval. 
                Submit an update request if you need to modify your bank details.
              </Alert>
            ) : (
              <Alert severity="warning" sx={{ mb: 3, fontSize: 16 }}>
                <strong>Bank details are pending verification.</strong> Once verified, they will be locked 
                and require admin approval for any changes.
              </Alert>
            )}

            {/* Bank Details Table */}
            <TableContainer>
              <Table sx={{ '& .MuiTableCell-root': { border: 'none' } }}>
                <TableBody>
                  <TableRow>
                    <TableCell sx={{ width: '30%', py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      Account Holder Name
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.bankAccountHolderName}
                    </TableCell>
                  </TableRow>
                  <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                    <TableCell sx={{ py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      Bank Name
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.bankName}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell sx={{ py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      Account Number
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.AccountNumber}
                    </TableCell>
                  </TableRow>
                  <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                    <TableCell sx={{ py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      Branch Name
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.branchName}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell sx={{ py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      Account Type
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.bankAccountType === "savings" ? "Savings" : "Current"}
                    </TableCell>
                  </TableRow>
                  <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                    <TableCell sx={{ py: 2, fontWeight: 'bold', fontSize: 16 }}>
                      IFSC Code
                    </TableCell>
                    <TableCell sx={{ py: 2, fontSize: 16 }}>
                      {bankDetails.branchIFSCCode}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>

            {/* Verification Details */}
            {bankDetails.razorpay_verification && (
              <>
                <Divider sx={{ my: 3 }} />
                <Typography variant="h6" sx={{ mb: 2, color: 'success.main' }}>
                  Verification Details
                </Typography>
                <TableContainer>
                  <Table sx={{ '& .MuiTableCell-root': { border: 'none' } }}>
                    <TableBody>
                      <TableRow>
                        <TableCell sx={{ width: '30%', py: 1, fontWeight: 'bold' }}>
                          Validation ID
                        </TableCell>
                        <TableCell sx={{ py: 1, fontFamily: 'monospace' }}>
                          {bankDetails.razorpay_verification.validation_id}
                        </TableCell>
                      </TableRow>
                      <TableRow sx={{ backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
                        <TableCell sx={{ py: 1, fontWeight: 'bold' }}>
                          Verified At
                        </TableCell>
                        <TableCell sx={{ py: 1 }}>
                          {new Date(bankDetails.razorpay_verification.verification_timestamp).toLocaleString()}
                        </TableCell>
                      </TableRow>
                      {bankDetails.razorpay_verification.amount_deposited && (
                        <TableRow>
                          <TableCell sx={{ py: 1, fontWeight: 'bold' }}>
                            Verification Amount
                          </TableCell>
                          <TableCell sx={{ py: 1, color: 'success.main', fontWeight: 'bold' }}>
                            ₹{(bankDetails.razorpay_verification.amount_deposited / 100).toFixed(2)}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </>
            )}

            <Divider sx={{ my: 3 }} />

            {/* Action Buttons */}
            <Box sx={{ display: "flex", justifyContent: "center", gap: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleUpdateRequest}
                startIcon={<EditIcon />}
                sx={{
                  fontSize: 16,
                  px: 3,
                  py: 1.5,
                  bgcolor: "hsla(120, 49%, 35%, 1)",
                  "&:hover": {
                    bgcolor: "rgb(39, 104, 39)",
                  },
                }}
              >
                Request Update
              </Button>
              
              {!bankDetails.isVerified && (
                <Button
                  variant="outlined"
                  color="success"
                  onClick={handleVerifyDetails}
                  disabled={verifying}
                  startIcon={<VerifiedIcon />}
                  sx={{ fontSize: 16, px: 3, py: 1.5 }}
                >
                  {verifying ? "Verifying..." : "Verify Now"}
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Create Bank Details Form */}
      {!loading && showCreateForm && (
        <ClubBankingForm
          onSuccess={handleCreateSuccess}
        />
      )}

      {/* Update Request Dialog */}
      <BankDetailsUpdateRequest
        open={updateRequestOpen}
        onClose={handleCloseUpdateRequest}
      />
    </Box>
  );
};

export default ClubBankingDetails;

const ClubBankingForm = ({ onSuccess }) => {
  const toast = UseToast();
  const [submitting, setSubmitting] = useState(false);
  const { control, handleSubmit, reset } = useForm({
    resolver: zodResolver(bankDetailsSchema),
    defaultValues: {
      bankName: "",
      AccountNumber: "",
      branchIFSCCode: "",
      branchName: "",
      bankAccountType: "",
      bankAccountHolderName: "",
    },
    mode: "onChange",
  });

  const onSubmit = async (data) => {
    try {
      setSubmitting(true);
      toast.info("Creating bank details...");

      const response = await Client.post("/club/profile/bankdetails", data);
      
      // Handle different status codes
      if (response.status === 422) {
        toast.error("Validation failed. Please check your input.");
        return;
      }
      
      if (response.status === 403) {
        toast.error("You don't have permission to perform this action.");
        return;
      }
      
      if (response.status === 400) {
        const errorData = response.data;
        
        if (errorData.error === "Bank details already exist") {
          toast.error("Bank details already exist. You can only create them once.");
          return;
        }
        
        if (errorData.error === "Invalid IFSC code format") {
          toast.error(`Invalid IFSC code: ${errorData.details || "Please check the format"}`);
        } else if (errorData.error === "Bank account verification failed") {
          toast.error("Bank account verification failed. Please check your details and try again.");
          
          if (errorData.details?.reason) {
            setTimeout(() => {
              toast.info(`Reason: ${errorData.details.reason}`);
            }, 1000);
          }
        } else {
          toast.error(errorData.error || "An error occurred while processing your request.");
        }
        return;
      }
      
      if (!response.data.success) {
        toast.error(response.data.error || "An error occurred while processing your request.");
        return;
      }
      
      // Success handling
      const responseData = response.data.data;
      
      toast.success("Bank details created successfully! They are now locked and require admin approval for any changes.");
      
      // Show verification details if available
      if (responseData.verification_details) {
        const details = responseData.verification_details;
        setTimeout(() => {
          toast.info(`Verification completed! Amount deposited: ${details.amount_deposited}`);
        }, 1000);
      }
      
      reset();
      if (onSuccess) {
        onSuccess();
      }
      
    } catch (error) {
      console.error("Error in onSubmit:", error);
      
      if (error.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        
        if (status === 400 && errorData.error === "Bank details already exist") {
          toast.error("Bank details already exist. You can only create them once.");
        } else if (status === 400 && errorData.error) {
          toast.error(errorData.error);
        } else if (status === 422) {
          toast.error("Please check your input and try again.");
        } else if (status === 403) {
          toast.error("You don't have permission to perform this action.");
        } else {
          toast.error("Failed to create bank details. Please try again later.");
        }
      } else {
        toast.error("Network error. Please check your connection and try again.");
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card elevation={3}>
      <CardContent sx={{ p: 3 }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid
            container
            spacing={3}
            sx={{
              ".MuiFormControl-root": { mt: "4px !important" },
              ".MuiInputBase-input": { py: 1.5 },
              ".MuiAutocomplete-input": { p: "4px !important" },
              ".MuiGrid-root .MuiGrid-item": { pt: "0px !important" },
              position: "relative",
            }}
          >
            {submitting && (
              <Box
                sx={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 1000,
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  width: "100%",
                  height: "100%",
                  backgroundColor: "rgba(255, 255, 255, 0.8)",
                }}
              >
                <Spinner />
              </Box>
            )}

            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h4" sx={{ mb: 2 }}>
                Create Bank Details
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3,fontSize:"1rem" }}>
                <strong>Important:</strong> You can only create bank details once. After creation, 
                they will be locked and any changes will require admin approval.
              </Alert>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="bankAccountHolderName"
                control={control}
                title="Bank Account Holder Name"
                placeholder="Enter Bank Account Holder Name"
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="bankName"
                control={control}
                title="Bank Name"
                placeholder="Enter Bank Name"
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormNumberField
                name="AccountNumber"
                minLength={9}
                maxLength={18}
                control={control}
                title="Account Number"
                placeholder="Enter Account Number"
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="branchName"
                control={control}
                title="Branch Name"
                placeholder="Enter Branch Name"
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormSelectField
                name="bankAccountType"
                control={control}
                title="Bank Account Type"
                placeholder="Select Bank Account Type"
                required
                options={[
                  { value: "savings", label: "Savings" },
                  { value: "current", label: "Current" },
                ]}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormTextField
                name="branchIFSCCode"
                control={control}
                title="Branch IFSC Code"
                placeholder="Enter Branch IFSC Code"
                required
              />
            </Grid>

            <Box sx={{ display: "flex", justifyContent: "center", width: "100%", mt: 4 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={submitting}
                sx={{
                  fontSize: 16,
                  px: 4,
                  py: 1.5,
                  bgcolor: "hsla(120, 49%, 35%, 1)",
                  "&:hover": {
                    bgcolor: "rgb(39, 104, 39)",
                  },
                  "&:disabled": {
                    bgcolor: "rgba(0, 0, 0, 0.12)",
                  },
                }}
                size="large"
              >
                {submitting ? "Creating..." : "Create Bank Details"}
              </Button>
            </Box>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};