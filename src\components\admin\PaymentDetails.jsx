import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Card,
  CardContent,
  Autocomplete,
  CircularProgress,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  Divider,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link, useParams } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import PaymentIcon from "@mui/icons-material/Payment";
import PersonIcon from "@mui/icons-material/Person";
import EventIcon from "@mui/icons-material/Event";
import { RestartAlt } from "@mui/icons-material";
import AccountBalanceIcon from "@mui/icons-material/AccountBalance";
import MonetizationOnIcon from "@mui/icons-material/MonetizationOn";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PendingIcon from "@mui/icons-material/Pending";
import ErrorIcon from "@mui/icons-material/Error";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const  PaymentTournamentDetails = () => {
  const [clubPayments, setClubPayments] = useState([]);
  const [playerPayments, setPlayerPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedTournament, setSelectedTournament] = useState(null);
  const [summaryStats, setSummaryStats] = useState({
    totalPayers: 0,
    totalAmount: 0,
    currency: "INR",
    successfulPayments: 0,
    pendingPayments: 0,
    failedPayments: 0,
  });
  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    paymentType: "all",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();
  const {title:id}=useParams();

  // Payout related states
  const [payoutData, setPayoutData] = useState(null);
  const [payoutLoading, setPayoutLoading] = useState(false);
  const [showPayoutDialog, setShowPayoutDialog] = useState(false);
  const [payoutCalculation, setPayoutCalculation] = useState(null);
  const [tournamentData, setTournamentData] = useState(null);
  const [clubPayouts, setClubPayouts] = useState([]);
  const [payoutStatusLoading, setPayoutStatusLoading] = useState({});

  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };


  // Define fetchPayments before using it in useEffect
  const fetchPayments = useCallback(async (pageNumber) => {
      setLoading(true);
      try {
        const params = {
          page: pageNumber,
          limit,
          tournamentTitle:id,
        };

        const response = await Client.get("/admin/details/tournament/payment", {
          params,
        });

        if (response.status === 204) {
          toast.info("No payment records found");
          setClubPayments([]);
          setPlayerPayments([]);
          setTotalPages(0);
          setPage(1);
          setSummaryStats({
            totalPayers: 0,
            totalAmount: 0,
            currency: "INR",
            successfulPayments: 0,
            pendingPayments: 0,
            failedPayments: 0,
          });
          return;
        }

        if (!response.data.success) {
          toast.info("No payment records found");
          return;
        }

        const paymentsData = response.data.data.payments;

        // Split payments by type
        const clubPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "club"
        );
        const playerPaymentsData = paymentsData.filter(
          (payment) => payment.paymentType === "player"
        );

        setClubPayments(clubPaymentsData);
        setPlayerPayments(playerPaymentsData);

        const totalPage = Math.ceil(response.data.data.total / limit);
        setTotalPages(totalPage);

        // Calculate and set summary statistics
        const stats = {
          totalPayers: 0,
          totalAmount: 0,
          currency: "INR",
          successfulPayments: 0,
          pendingPayments: 0,
          failedPayments: 0,
        };
        stats.totalPayers = paymentsData.reduce((acc, payment) => {
          if (
            search.paymentType === "all" ||
            payment.paymentType === search.paymentType
          ) {
            if (payment.paymentType === "club") {
              acc += payment.playersCount || 0;
            } else if (payment.paymentType === "player") {
              acc += 1;
            }
          }
          return acc;
        }, 0);

        paymentsData.forEach((payment) => {
          // Add to total amount
          stats.totalAmount += parseFloat(payment.paymentAmount || 0);

          // Count by status
          const status = payment.paymentStatus?.toLowerCase();
          if (status === "paid" || status === "success") {
            stats.successfulPayments++;
          } else if (status === "pending") {
            stats.pendingPayments++;
          } else if (status === "failed") {
            stats.failedPayments++;
          }

          // Set currency if available
          if (payment.paymentCurrency) {
            stats.currency = payment.paymentCurrency;
          }
        });

        setSummaryStats(stats);
      } catch (error) {
        if (error instanceof AxiosError && error.response) {
          toast.info(error.response.data.error);
          return;
        }
        console.error("Error fetching payment history:", error);
        toast.info("An error occurred. Please try again later.");
      } finally {
        setLoading(false);
      }
  }, [id, limit, search.paymentType]);
  // Fetch tournament data for payout calculations
  const fetchTournamentData = useCallback(async () => {
    try {
      const response = await Client.get(`/admin/tournament/${encodeURIComponent(id)}`);
      if (response.data.success) {
        setTournamentData(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching tournament data:", error);
    }
  }, [id]);

    // Fetch existing club payouts for this tournament
  const fetchClubPayouts = useCallback(async () => {
    try {
      const response = await Client.get("/payout/list", {
        params: { tournament_id: id }
      });
      if (response.data.success) {
        setClubPayouts(response.data.data.payouts || []);
      }
    } catch (error) {
      console.error("Error fetching club payouts:", error);
    }
  }, [id]);

  useEffect(() => {
      fetchPayments(1);
      fetchTournamentData();
      fetchClubPayouts();
  }, [fetchPayments, fetchTournamentData, fetchClubPayouts]);





  // Calculate payout preview for a club
  const calculatePayoutPreview = async (clubId, tournamentId) => {
    try {
      setPayoutLoading(true);
      const response = await Client.post(`/payout/calculate/${encodeURIComponent(tournamentId)}`, {
        club_id: clubId
      });

      if (response.data.success) {
        setPayoutCalculation(response.data.data);
        setShowPayoutDialog(true);
      } else {
        toast.error(response.data.error || "Failed to calculate payout");
      }
    } catch (error) {
      console.error("Error calculating payout:", error);
      toast.error("Failed to calculate payout");
    } finally {
      setPayoutLoading(false);
    }
  };

  // Process payout for a club
  const processPayout = async (clubId, tournamentId, urgent = false) => {
    try {
      setPayoutLoading(true);
      const response = await Client.post("/payout/create", {
        tournament_id: tournamentId,
        club_id: clubId,
        urgent
      });

      if (response.data.success) {
        toast.success("Payout created successfully!");
        setShowPayoutDialog(false);
        fetchClubPayouts(); // Refresh payout list
      } else {
        toast.error(response.data.error || "Failed to create payout");
      }
    } catch (error) {
      console.error("Error creating payout:", error);
      toast.error("Failed to create payout");
    } finally {
      setPayoutLoading(false);
    }
  };

  // Check payout status
  const checkPayoutStatus = async (payoutId) => {
    try {
      setPayoutStatusLoading(prev => ({ ...prev, [payoutId]: true }));
      const response = await Client.get(`/payout/status/${payoutId}`);

      if (response.data.success) {
        // Update the payout in the list
        setClubPayouts(prev =>
          prev.map(payout =>
            payout.payout_id === payoutId
              ? { ...payout, ...response.data.data }
              : payout
          )
        );
        toast.success("Payout status updated");
      }
    } catch (error) {
      console.error("Error checking payout status:", error);
      toast.error("Failed to check payout status");
    } finally {
      setPayoutStatusLoading(prev => ({ ...prev, [payoutId]: false }));
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Get status color for payout status
  const getPayoutStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'processed':
      case 'completed':
        return 'success';
      case 'pending':
      case 'queued':
        return 'warning';
      case 'failed':
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get status icon for payout status
  const getPayoutStatusIcon = (status) => {
    switch (status?.toLowerCase()) {
      case 'processed':
      case 'completed':
        return <CheckCircleIcon fontSize="small" />;
      case 'pending':
      case 'queued':
        return <PendingIcon fontSize="small" />;
      case 'failed':
      case 'cancelled':
        return <ErrorIcon fontSize="small" />;
      default:
        return <PendingIcon fontSize="small" />;
    }
  };

  const fetchPaymentReport = async () => {
    // return;
    // setLoading(true);

    try {
      const response = await Client.get("/report/payment", {
        params: { ...search },
        responseType: "blob",
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "Players_report.xlsx");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

    console.log("clubPayments",clubPayments)

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pt: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {(clubPayments?.length > 0 || playerPayments?.length>0) && 
      <Box mb={2} sx={{display:'flex',justifyContent:'flex-end',alignItems:'center'}}>
        <Button size="small" disabled={!clubPayments?.length && !playerPayments?.length}variant="contained" onClick={fetchPaymentReport}>Download report</Button>
      </Box>}

      {/* Summary Statistics */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment Summary
        </Typography>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Total Payers
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.totalPayers}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <PersonIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Players
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.totalAmount.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                  })}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Collected
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="primary">
                  Tournament
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    mt: 1,
                    fontWeight: "bold",
                    textTransform: "capitalize",
                    textAlign: "center",
                  }}
                >
                  {selectedTournament && selectedTournament.title
                    ? selectedTournament.title.replace(/-/g, " ")
                    : "All Tournaments"}
                </Typography>
                <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                  <EventIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  {selectedTournament && selectedTournament.startDate
                    ? new Date(selectedTournament.startDate).toLocaleDateString(
                        "en-US",
                        { day: "2-digit", month: "short", year: "numeric" }
                      )
                    : "All Dates"}
                </Typography>
              </CardContent>
            </Card>
          </Grid>

           <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
              <CardContent>
                <Typography variant="subtitle2" color="secondary">
                  Total Refund Amount
                </Typography>
                <Typography
                  variant="h4"
                  sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
                >
                  {summaryStats.currency}{" "}
                  {summaryStats.refoundAmount || 0}
                </Typography>
                <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                  <PaymentIcon
                    fontSize="small"
                    sx={{ verticalAlign: "middle", mr: 0.5 }}
                  />
                  Refunded
                </Typography>
              </CardContent>
            </Card>
          </Grid>

        </Grid>
      </Paper>

      {/* Payout Management Section */}
      {clubPayments?.length > 0 && (
        <Paper
          sx={{ mb: 3, p: 3, bgcolor: "#fff8e1", borderRadius: 2, boxShadow: 2 }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{ fontWeight: "bold", color: "#f57c00", mb: 2 }}
          >
            <AccountBalanceIcon sx={{ mr: 1, verticalAlign: "middle" }} />
            Club Payout Management
          </Typography>

          {/* Club Payout Actions */}
          <Grid container spacing={2} sx={{ mb: 3 }}>
            {clubPayments.map((clubPayment) => {
              const existingPayout = clubPayouts.find(p => p.club_id === clubPayment.clubId);

              return (
                <Grid item xs={12} md={6} key={clubPayment.clubId}>
                  <Card sx={{ bgcolor: "#f5f5f5", border: "1px solid #e0e0e0" }}>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="h6" fontWeight="medium">
                          {clubPayment.clubName}
                        </Typography>
                        {existingPayout && (
                          <Chip
                            icon={getPayoutStatusIcon(existingPayout.status)}
                            label={existingPayout.status?.toUpperCase()}
                            color={getPayoutStatusColor(existingPayout.status)}
                            size="small"
                          />
                        )}
                      </Box>

                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Payment Amount: ₹{clubPayment.paymentAmount}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Players: {clubPayment.playersCount}
                      </Typography>

                      <Box mt={2} display="flex" gap={1}>
                        {!existingPayout ? (
                          <Button
                            variant="contained"
                            color="primary"
                            size="small"
                            startIcon={<MonetizationOnIcon />}
                            onClick={() => calculatePayoutPreview(clubPayment.clubId, id)}
                            disabled={payoutLoading}
                          >
                            Calculate Payout
                          </Button>
                        ) : (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={payoutStatusLoading[existingPayout.payout_id] ?
                              <CircularProgress size={16} /> :
                              <SearchIcon />
                            }
                            onClick={() => checkPayoutStatus(existingPayout.payout_id)}
                            disabled={payoutStatusLoading[existingPayout.payout_id]}
                          >
                            Check Status
                          </Button>
                        )}
                      </Box>

                      {existingPayout && (
                        <Box mt={2} p={2} bgcolor="#e8f5e9" borderRadius={1}>
                          <Typography variant="body2" fontWeight="medium">
                            Payout Details:
                          </Typography>
                          <Typography variant="body2">
                            Amount: ₹{existingPayout.amount}
                          </Typography>
                          <Typography variant="body2">
                            Status: {existingPayout.status}
                          </Typography>
                          <Typography variant="body2">
                            Created: {formatDate(existingPayout.created_at)}
                          </Typography>
                          {existingPayout.reference_id && (
                            <Typography variant="body2">
                              Reference: {existingPayout.reference_id}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              );
            })}
          </Grid>

          {clubPayouts.length > 0 && (
            <Box>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                All Payouts for this Tournament
              </Typography>
              <DynamicTable
                columns={[
                  {
                    id: "club_name",
                    label: "Club Name",
                    format: (_, payout) => payout.club_name || "N/A",
                  },
                  {
                    id: "amount",
                    label: "Amount",
                    format: (_, payout) => `₹${payout.amount}`,
                  },
                  {
                    id: "status",
                    label: "Status",
                    format: (_, payout) => (
                      <Chip
                        icon={getPayoutStatusIcon(payout.status)}
                        label={payout.status?.toUpperCase()}
                        color={getPayoutStatusColor(payout.status)}
                        size="small"
                      />
                    ),
                  },
                  {
                    id: "created_at",
                    label: "Created",
                    format: (_, payout) => formatDate(payout.created_at),
                  },
                  {
                    id: "reference_id",
                    label: "Reference",
                    format: (_, payout) => payout.reference_id || "N/A",
                  },
                ]}
                data={clubPayouts}
                loading={false}
                showDetailsButton={false}
                tableContainerProps={{
                  sx: {
                    minHeight: "200px",
                    maxHeight: "300px",
                  },
                }}
              />
            </Box>
          )}
        </Paper>
      )}

      {/* Club Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "club") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Club Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "clubName",
                label: "ClubName",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/clubs/${payment.clubId}`}
                    sx={{
                      color: "#1976d2",
                      textDecoration: "none",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.clubName || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "playersCount",
                label: "Players Count",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    fontSize={"16px"}
                    fontWeight="medium"
                  >
                    {payment.playersCount || "0"}
                  </Typography>
                ),
              },
              {
                id: "registrationType",
                label: "Registration Type",
                format: (_, payment) => payment.registrationType || "N/A",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "paid"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={clubPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}

            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Player Payment History Table */}
      {(search.paymentType === "all" || search.paymentType === "player") && (
        <>
          <Typography
            variant="h6"
            gutterBottom
            sx={{ mt: 4, mb: 2, fontWeight: "bold", color: "#3f51b5" }}
          >
            Player Payments
          </Typography>
          <DynamicTable
            columns={[
              {
                id: "playerName",
                label: "Player Name",
                format: (_, payment) => (
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: "medium", fontSize: "16px" }}
                  >
                    {payment.playerName || "Unknown Player"}
                  </Typography>
                ),
              },
              {
                id: "cbid",
                label: "CBID",
                format: (_, payment) => (
                  <Typography
                    component={Link}
                    to={`/players/${payment.player?.cbid}`}
                    sx={{ color: "#1976d2", textDecoration: "none" }}
                  >
                    {payment.cbid || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "transactionId",
                label: "Transaction ID",
                format: (_, payment) => payment.paymentTransactionId || "N/A",
              },
              {
                id: "date",
                label: "Date",
                format: (_, payment) => formatDate(payment.paymentDate),
              },
              {
                id: "amount",
                label: "Amount",
                format: (_, payment) => (
                  <Typography variant="h6" fontSize={"16px"}>
                    {payment.paymentCurrency || "INR"}{" "}
                    {payment.paymentAmount || "0.00"}
                  </Typography>
                ),
              },
              {
                id: "paymentMethod",
                label: "Payment Method",
                format: (_, payment) => payment.paymentMode || "Online",
              },
              {
                id: "paymentStatus",
                label: "Status",
                format: (_, payment) => (
                  <Typography
                    variant="body1"
                    sx={{
                      color:
                        payment.paymentStatus?.toLowerCase() === "paid"
                          ? "green"
                          : payment.paymentStatus?.toLowerCase() === "pending"
                          ? "orange"
                          : "red",
                      fontWeight: "medium",
                    }}
                  >
                    {payment.paymentStatus || "N/A"}
                  </Typography>
                ),
              },
              {
                id: "tournament",
                label: "Tournament",
                format: (_, payment) =>
                  payment?.tournament?.title || payment.tournamentTitle ? (
                    <Link
                      to={`/tournaments/${
                        payment?.tournament?.title || payment.tournamentTitle
                      }`}
                      style={{ textDecoration: "none", color: "inherit" }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          textTransform: "capitalize",
                          fontWeight: "medium",
                          textWrap: "balance",
                          fontSize: "16px",
                        }}
                      >
                        {(payment?.tournament?.title || payment.tournamentTitle)
                          .toLowerCase()
                          .replace(/-/g, " ")}
                      </Typography>
                    </Link>
                  ) : (
                    "N/A"
                  ),
              },
            ]}
            data={playerPayments}
            loading={loading}
            page={page}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            detailsPath="/players/"
            idField="id"
            showDetailsButton={false}

            tableContainerProps={{
              sx: {
                minHeight: "200px",
                maxHeight: "400px",
              },
            }}
          />
        </>
      )}

      {/* Payout Calculation Dialog */}
      <Dialog
        open={showPayoutDialog}
        onClose={() => setShowPayoutDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h6" fontWeight="bold">
            Payout Calculation Preview
          </Typography>
        </DialogTitle>
        <DialogContent>
          {payoutCalculation && (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Review the payout calculation before processing the payment.
              </Alert>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ bgcolor: "#f5f9ff" }}>
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Tournament Details
                      </Typography>
                      <Typography variant="body2">
                        Name: {payoutCalculation.tournament?.name}
                      </Typography>
                      <Typography variant="body2">
                        Participants: {payoutCalculation.tournament?.participants}
                      </Typography>
                      <Typography variant="body2">
                        Entry Fee: ₹{payoutCalculation.tournament?.entry_fee}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card sx={{ bgcolor: "#f1f8e9" }}>
                    <CardContent>
                      <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                        Payout Summary
                      </Typography>
                      <Typography variant="body2">
                        Total Collected: ₹{payoutCalculation.calculation?.total_collected}
                      </Typography>
                      <Typography variant="body2">
                        Platform Fee: ₹{payoutCalculation.calculation?.platform_fee}
                      </Typography>
                      <Typography variant="body2">
                        Processing Fee: ₹{payoutCalculation.calculation?.processing_fee}
                      </Typography>
                      <Typography variant="h6" color="primary" fontWeight="bold">
                        Club Payout: ₹{payoutCalculation.calculation?.club_payout}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>

              <Box sx={{ bgcolor: "#fff3e0", p: 2, borderRadius: 1, mb: 2 }}>
                <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
                  Breakdown:
                </Typography>
                {payoutCalculation.breakdown && Object.entries(payoutCalculation.breakdown).map(([key, value]) => (
                  <Box key={key} display="flex" justifyContent="space-between" mb={0.5}>
                    <Typography variant="body2">{key}:</Typography>
                    <Typography variant="body2" fontWeight="medium">{value}</Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPayoutDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              if (payoutCalculation?.tournament?.id) {
                processPayout(
                  payoutCalculation.club_id || clubPayments[0]?.clubId,
                  payoutCalculation.tournament.id,
                  false
                );
              }
            }}
            disabled={payoutLoading}
            startIcon={payoutLoading ? <CircularProgress size={16} /> : <MonetizationOnIcon />}
          >
            {payoutLoading ? "Processing..." : "Process Payout"}
          </Button>
          <Button
            variant="outlined"
            color="warning"
            onClick={() => {
              if (payoutCalculation?.tournament?.id) {
                processPayout(
                  payoutCalculation.club_id || clubPayments[0]?.clubId,
                  payoutCalculation.tournament.id,
                  true
                );
              }
            }}
            disabled={payoutLoading}
            startIcon={payoutLoading ? <CircularProgress size={16} /> : <MonetizationOnIcon />}
          >
            {payoutLoading ? "Processing..." : "Urgent Payout"}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PaymentTournamentDetails;