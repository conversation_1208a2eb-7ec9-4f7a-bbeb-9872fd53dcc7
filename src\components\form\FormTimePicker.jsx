import React from "react";
import {
  Grid,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
  Typography,
  Box,
} from "@mui/material";
import { Controller } from "react-hook-form";

/**
 * FormTimePicker - A time picker component for React Hook Form
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {Object} props.control - React Hook Form control object
 * @param {string} props.title - Label for the time picker
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Whether the field is required
 * @param {Object} props.rules - Additional validation rules
 * @param {string} props.defaultValue - Default time value (format: "hh:mm AM/PM")
 * @param {Object} props.sx - Additional styles for the component
 */
const FormTimePicker = ({
  name,
  control,
  title,
  placeholder,
  required = false,
  rules = {},
  defaultValue = "09:00 AM",
  sx = {},
}) => {
  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);

  // Generate minutes (00-55 in 5-minute increments)
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5).map((min) =>
    min.toString().padStart(2, "0")
  );

  // Parse the time string into hour, minute, and period
  const parseTime = (timeString = "09:00 AM") => {
    if (!timeString) return { hour: 9, minute: "00", period: "AM" };

    const match = timeString.match(/^(\d{1,2}):(\d{2})\s?(AM|PM)?$/i);
    if (!match) return { hour: 9, minute: "00", period: "AM" };

    let [, h, m, p = "AM"] = match;
    let hour = parseInt(h, 10) || 12;
    let minute = m || "00";
    let period = (p || "AM").toUpperCase();

    return { hour, minute, period };
  };

  // Combine required rule with other rules
  const validationRules = required
    ? { required: `${title} is required`, ...rules }
    : rules;

  return (
    <Box sx={{ mb: 2, ...sx }}>
      {title && (
        <Typography
          variant="h6"
          sx={{
            fontWeight: 400,
            width: "fit-content",
            fontSize: 20,
            mb: 0.5,
            color: "text.primary",
            "&::after": required
              ? {
                  content: '" *"',
                  color: "error.main",
                }
              : {},
          }}
        >
          {title}
        </Typography>
      )}

      <Controller
        name={name}
        control={control}
        rules={validationRules}
        defaultValue={defaultValue}
        render={({ field: { value, onChange }, fieldState: { error } }) => {
          const currentTime = parseTime(value);

          const handleTimeChange = (field, newValue) => {
            const updatedTime = { ...currentTime, [field]: newValue };

            // Format hour with leading zeros
            const finalHour = parseInt(updatedTime.hour, 10) || 12; // Default to 12 if invalid
            const paddedHour = finalHour.toString().padStart(2, "0");

            // Ensure valid minute value with leading zeros
            const finalMinute = updatedTime.minute || "00";

            // Format time string according to the expected format: "hh:mm AM/PM"
            const formattedTime = `${paddedHour}:${finalMinute} ${updatedTime.period}`;

            onChange(formattedTime);
          };

          return (
            <>
              <Grid container spacing={1} alignItems="center">
                <Grid item xs={4}>
                  <FormControl fullWidth error={!!error}>
                    <Select
                      value={currentTime.hour}
                      onChange={(e) => handleTimeChange("hour", e.target.value)}
                      displayEmpty
                      renderValue={(selected) => (selected ? selected : "Hour")}
                      size="small"
                    >
                      {hours.map((hour) => (
                        <MenuItem key={hour} value={hour}>
                          {hour}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4}>
                  <FormControl fullWidth error={!!error}>
                    <Select
                      value={currentTime.minute}
                      onChange={(e) =>
                        handleTimeChange("minute", e.target.value)
                      }
                      displayEmpty
                      renderValue={(selected) => (selected ? selected : "Min")}
                      size="small"
                    >
                      {minutes.map((minute) => (
                        <MenuItem key={minute} value={minute}>
                          {minute}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={4}>
                  <FormControl fullWidth error={!!error}>
                    <Select
                      value={currentTime.period}
                      onChange={(e) =>
                        handleTimeChange("period", e.target.value)
                      }
                      size="small"
                    >
                      <MenuItem value="AM">AM</MenuItem>
                      <MenuItem value="PM">PM</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              {error && (
                <FormHelperText error sx={{ ml: 1.5 }}>
                  {error.message}
                </FormHelperText>
              )}
            </>
          );
        }}
      />
    </Box>
  );
};

export default FormTimePicker;
