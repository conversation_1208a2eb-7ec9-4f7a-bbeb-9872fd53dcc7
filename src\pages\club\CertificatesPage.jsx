import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useParams } from "react-router-dom";
import { PDFViewer, PDFDownloadLink } from "@react-pdf/renderer";
import { useForm, Controller, get } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Box,
  CircularProgress,
  Alert,
  IconButton,
  Avatar,
  LinearProgress,
  Chip,
} from "@mui/material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import BackButton from "../../components/common/BackButton";
import CertificateTemplate from "../../components/certificates/CertificateTemplate";
// import CertificateTemplate2 from "../../components/common/CertificateTemplate2";
// import CertificateTemplate3 from "../../components/common/CertificateTemplate3";
import { CloudUpload, Delete, Visibility, Refresh } from "@mui/icons-material";
import FormTextField from "../../components/form/FormTextField";

const certificateSchema = z.object({
  organization: z.string().min(1, "Organization name is required"),
  tournamentTitle: z.string().min(1, "Tournament title is required"),
  subtitle: z.string().min(1, "Subtitle is required"),
  tournamentDate: z.string().min(1, "Tournament date is required"),
  venue: z.string().min(1, "Venue is required"),
  poweredBy: z.string().min(1, "Powered by is required"),
  topNumber: z.string().min(1, "Top number is required"),
  templateId: z.number().min(1, "Please select a template"),
  chiefArbiterSignature: z.any().optional(),
  tournamentDirectorSignature: z.any().optional(),
  chiefArbiterSignaturePreview: z.string().optional(),
  tournamentDirectorSignaturePreview: z.string().optional(),
});

// Validate template components on import
const validateTemplateComponent = (component, name) => {
  if (!component || typeof component !== "function") {
    console.error(`Template component ${name} is not valid:`, component);
    return false;
  }
  return true;
};

const CERTIFICATE_TEMPLATES = {
  1: {
    name: "Template 1",
    description: "template with trophy on background",
    component: CertificateTemplate,
    isValid: validateTemplateComponent(
      CertificateTemplate,
      "CertificateTemplate"
    ),
  },
  // 2: {
  //   name: "Template 2",
  //   description: "Vibrant modern design with orange accents",
  //   component: CertificateTemplate2,
  //   isValid: validateTemplateComponent(
  //     CertificateTemplate2,
  //     "CertificateTemplate2"
  //   ),
  // },
  // 3: {
  //   name: "Template 3",
  //   description: "Sophisticated design with brown borders",
  //   component: CertificateTemplate3,
  //   isValid: validateTemplateComponent(
  //     CertificateTemplate3,
  //     "CertificateTemplate3"
  //   ),
  // },
};

const CertificatesPage = () => {
  const { title } = useParams();

  const toast = UseToast();

  const [tournament, setTournament] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState(1);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    getValues,
    reset,
  } = useForm({
    resolver: zodResolver(certificateSchema),
    defaultValues: {
      organization: "Sample Organization Name",
      tournamentTitle: "Sample Tournament Championship",
      subtitle: "Annual Chess Tournament 2024",
      poweredBy: "ChessBrigade.com",
      tournamentDate: new Date().toLocaleDateString(),
      venue: "Sample Venue, Sample City",
      topNumber: "10",
      templateId: 1,
      chiefArbiterSignature: null,
      tournamentDirectorSignature: null,
      chiefArbiterSignaturePreview: "",
      tournamentDirectorSignaturePreview: "",
    },
    mode: "onChange",
  });
  const [pdfData, setPdfData] = useState(getValues());
  const [isClient, setIsClient] = useState(false);
  const [saving, setSaving] = useState(false);
  const [templateSwitching, setTemplateSwitching] = useState(false);
  const [pdfError, setPdfError] = useState(null);
  const [pdfKey, setPdfKey] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  const watchedValues = watch();

  const fetchTournamentDetails = useCallback(async () => {
    try {
      setLoading(true);
      const encodedTitle = encodeURIComponent(title);
      const response = await Client.get(`/tournament/${encodedTitle}`);

      if (response.data.success) {
        const tournamentData = response.data.data;
        setTournament(tournamentData);
        console.log("Tournament Data:", tournamentData.certificateData);
        if (tournamentData.certificateData) {
          setSelectedTemplate(tournamentData.certificateData.templateId);
          reset(tournamentData.certificateData);
          setPdfData(tournamentData.certificateData);
          console.log('jagadeesh',tournamentData.certificateData)
        } else {
          const initialData = {
            ...getValues(),
            organization: tournamentData.organizerName || "",
            tournamentTitle: tournamentData.title.replace(/-/g, " ") || "",
            subtitle: tournamentData.description || "",
            tournamentDate:
              new Date(tournamentData.startDate).toLocaleDateString() || "",
            venue: `${tournamentData.venueAddress || ""}, ${
              tournamentData.city || ""
            }`,
          };

          reset(initialData);
          setPdfData(initialData); // Initialize PDF data too
        }
      } else {
        toast.error("Failed to fetch tournament details");
      }
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("Error fetching tournament details");
    } finally {
      setLoading(false);
    }
  }, [title, toast]);

  const handleManualRefresh = useCallback(() => {
    setRefreshing(true);
    setPdfError(null);

    // Update PDF data with current form data
    setPdfData({ ...getValues() });
    setPdfKey((prev) => prev + 1);

    // Show refreshing state briefly
    setTimeout(() => {
      setRefreshing(false);
    }, 500);
  }, [getValues]);

  useEffect(() => {
    setIsClient(true);
    fetchTournamentDetails();
  }, [title]);

  // Clear errors when template changes successfully
  useEffect(() => {
    if (!templateSwitching && selectedTemplate) {
      // Clear any previous errors after a successful template switch
      const timer = setTimeout(() => {
        setPdfError(null);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [selectedTemplate]);
  const hasUnsavedChanges = useMemo(() => {
    return JSON.stringify(getValues()) !== JSON.stringify(pdfData);
  }, [getValues, pdfData]);

  // Global error handler for PDF-related errors
  useEffect(() => {
    const handleError = (event) => {
      if (
        event.error &&
        (event.error.message?.includes("Eo is not a function") ||
          event.error.message?.includes("PDF") ||
          event.error.stack?.includes("reconciler") ||
          event.error.stack?.includes("PDFDownloadLink") ||
          event.error.stack?.includes("PDFViewer"))
      ) {
        console.error("PDF-related error caught:", event.error);
        setPdfError(
          "PDF rendering error occurred. Please try refreshing the preview."
        );
        event.preventDefault();
        return false;
      }
    };

    const handleUnhandledRejection = (event) => {
      if (
        event.reason &&
        typeof event.reason === "object" &&
        (event.reason.message?.includes("PDF") ||
          event.reason.message?.includes("Eo is not a function") ||
          event.reason.stack?.includes("reconciler"))
      ) {
        console.error("PDF-related promise rejection:", event.reason);
        setPdfError(
          "PDF rendering error occurred. Please try refreshing the preview."
        );
        event.preventDefault();
        return false;
      }
    };

    window.addEventListener("error", handleError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, []);

  // Updated form submission function in your main component
  const handleSaveCertificateConfig = async () => {
    try {
      setSaving(true);
      const encodedTitle = encodeURIComponent(title);
      const formData = getValues();
      const {
        chiefArbiterSignature,
        tournamentDirectorSignature,
        tournamentDirectorSignaturePreview,
        chiefArbiterSignaturePreview,
        ...rest
      } = formData;
      // The signature URLs are already set from the upload process
      const configData = {
        templateId: selectedTemplate,
        certificateData: {
          ...rest,
          // URLs are already stored in form data from upload
          chiefArbiterSignatureUrl: formData.chiefArbiterSignature,
          tournamentDirectorSignatureUrl: formData.tournamentDirectorSignature,
        },
        tournamentTitle: title,
      };

      const response = await Client.patch(
        `/tournament/${encodedTitle}/certificate-config`,
        configData
      );

      if (response.data.success) {
        toast.success("Certificate configuration saved successfully!");
      } else {
        toast.error("Failed to save certificate configuration");
      }
    } catch (error) {
      console.error("Error saving certificate config:", error);
      toast.error("Error saving certificate configuration");
    } finally {
      setSaving(false);
    }
  };
  const handleGenerateCertificate = async () => {
    try {
      const encodedTitle = encodeURIComponent(title);

      const generateData = {
        templateId: selectedTemplate,
        certificateData: getValues(),
      };

      const response = await Client.post(
        `/tournament/${encodedTitle}/generate-certificate`,
        generateData
      );

      if (response.data.success) {
        toast.success("Certificate generated successfully!");
        // You can add additional logic here like downloading the certificate
      } else {
        toast.error("Failed to generate certificate");
      }
    } catch (error) {
      console.error("Error generating certificate:", error);
      toast.error(error.response.data.error || "Error generating certificate");
    }
  };

  const SelectedTemplateComponent =
    CERTIFICATE_TEMPLATES[selectedTemplate]?.component || CertificateTemplate;

  // Debug logging
  console.log("Selected template:", selectedTemplate);
  console.log("Template component:", SelectedTemplateComponent);
  console.log("Certificate data:", getValues());
  console.log("Template switching:", templateSwitching);
  console.log("PDF error:", pdfError);

  // Validate template component
  const currentTemplate = CERTIFICATE_TEMPLATES[selectedTemplate];
  const isValidTemplate =
    currentTemplate &&
    currentTemplate.isValid &&
    SelectedTemplateComponent &&
    typeof SelectedTemplateComponent === "function";

  // Safe PDF component wrapper
  const SafePDFComponent = useMemo(() => {
    const currentTemplate = CERTIFICATE_TEMPLATES[selectedTemplate];
    const SelectedTemplateComponent = currentTemplate?.component;

    if (!SelectedTemplateComponent || !currentTemplate?.isValid) {
      return null;
    }

    try {
      // Use pdfData instead of certificateData
      return <SelectedTemplateComponent data={pdfData} />;
    } catch (error) {
      console.error("PDF component creation error:", error);
      setPdfError(error.message);
      return null;
    }
  }, [selectedTemplate, pdfData, pdfKey]);

  if (loading) {
    return (
      <Container
        maxWidth="xl"
        sx={{ py: 4, display: "flex", justifyContent: "center" }}
      >
        <CircularProgress />
      </Container>
    );
  }

  if (!isClient) {
    return (
      <Container maxWidth="xl" sx={{ py: 4 }}>
        <Typography>Loading PDF Preview...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <BackButton />

      <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
        Certificate Templates - {tournament?.title}
      </Typography>

      <Grid container spacing={3}>
        {/* Left Panel - Configuration */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 3, height: "fit-content" }}>
            <Typography variant="h6" gutterBottom>
              Certificate Configuration
            </Typography>
            <Box
              component="form"
              onSubmit={handleSubmit(handleSaveCertificateConfig)}
            >
              {/* Template Selection */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Certificate Template *
                </Typography>
                <Controller
                  name="templateId"
                  control={control}
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth error={!!error}>
                      <Select {...field} displayEmpty sx={{ mb: 1 }}>
                        <MenuItem value={0} disabled>
                          Select a template
                        </MenuItem>
                        {Object.entries(CERTIFICATE_TEMPLATES).map(
                          ([id, template]) => (
                            <MenuItem key={id} value={parseInt(id)}>
                              <Box>
                                <Typography variant="body1">
                                  {template.name}
                                </Typography>
                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                >
                                  {template.description}
                                </Typography>
                              </Box>
                            </MenuItem>
                          )
                        )}
                      </Select>
                      {error && (
                        <Typography variant="caption" color="error">
                          {error.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Box>

              {/* Template switching indicator
            {templateSwitching && (
              <Alert severity="info" sx={{ mb: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <CircularProgress size={16} />
                  <Typography variant="body2">Switching template...</Typography>
                </Box>
              </Alert>
            )} */}
              {hasUnsavedChanges && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ fontSize: "0.875rem" }}>
                    You have unsaved changes. Click "Refresh Preview" to see
                    updates in the PDF.
                  </Typography>
                </Alert>
              )}

              {/* Error display */}
              {pdfError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="body2">{pdfError}</Typography>
                  <Button
                    size="small"
                    onClick={handleManualRefresh}
                    sx={{ mt: 1 }}
                  >
                    Retry
                  </Button>
                </Alert>
              )}

              <FormTextField
                name="organization"
                control={control}
                title="Organization Name"
                placeholder="Enter organization name"
                required
                specialCharAllowed
              />

              <FormTextField
                name="tournamentTitle"
                control={control}
                title="Tournament Title"
                placeholder="Enter tournament title"
                required
                specialCharAllowed
              />

              <FormTextField
                name="subtitle"
                control={control}
                title="Subtitle"
                placeholder="Enter tournament subtitle or description"
                required
                specialCharAllowed
              />

              <FormTextField
                name="tournamentDate"
                control={control}
                title="Tournament Date"
                placeholder="Enter tournament date"
                required
              />

              <FormTextField
                name="venue"
                control={control}
                title="Venue"
                placeholder="Enter venue address"
                required
                multiline
                rows={2}
                specialCharAllowed
              />

              <FormTextField
                name="poweredBy"
                control={control}
                title="Powered By"
                placeholder="Enter sponsor or platform name"
                required
                specialCharAllowed
              />

              <FormTextField
                name="topNumber"
                control={control}
                title="Top Player Number"
                placeholder="Enter number of top players"
                type="number"
                required
              />

              {/* Signature Upload Section */}
              <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Signature Uploads
              </Typography>

              <SignatureUpload
                setValue={setValue}
                watch={watch}
                name="chiefArbiterSignature"
                previewName="chiefArbiterSignaturePreview"
                title="Chief Arbiter Signature"
                tournamentId={tournament?.id}
              />

              <SignatureUpload
                setValue={setValue}
                watch={watch}
                name="tournamentDirectorSignature"
                previewName="tournamentDirectorSignaturePreview"
                title="Tournament Director Signature"
                tournamentId={tournament?.id}
              />

              {/* Action Buttons */}
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Button
                  variant="outlined"
                  type="submit"
                  disabled={saving || !isValid}
                  startIcon={saving ? <CircularProgress size={20} /> : null}
                >
                  {saving ? "Saving..." : "Save Configuration"}
                </Button>

                <Button
                  variant="contained"
                  onClick={handleGenerateCertificate}
                  color="primary"
                >
                  Generate Certificate
                </Button>

                {isValidTemplate && !pdfError && SafePDFComponent ? (
                  <PDFDownloadLink
                    key={`pdf-download-${pdfKey}`}
                    document={SafePDFComponent}
                    fileName={`certificate_template_${selectedTemplate}.pdf`}
                    style={{ textDecoration: "none" }}
                  >
                    {({ loading, error }) => (
                      <Button
                        variant="contained"
                        color="success"
                        disabled={loading || templateSwitching || error}
                        fullWidth
                      >
                        {error
                          ? "Download Error"
                          : loading
                          ? "Preparing..."
                          : templateSwitching
                          ? "Switching..."
                          : "Download Preview"}
                      </Button>
                    )}
                  </PDFDownloadLink>
                ) : (
                  <Button
                    variant="contained"
                    color="success"
                    disabled
                    fullWidth
                  >
                    Download Unavailable
                  </Button>
                )}
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Right Panel - Preview */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ p: 2, height: "80vh" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 2,
              }}
            >
              <Typography variant="h6">
                Live Preview - {CERTIFICATE_TEMPLATES[selectedTemplate]?.name}
              </Typography>

              {/* SOLUTION 9: Prominent refresh button */}
              <Button
                variant={hasUnsavedChanges ? "contained" : "outlined"}
                color={hasUnsavedChanges ? "primary" : "inherit"}
                onClick={handleManualRefresh}
                disabled={refreshing}
                startIcon={refreshing ? <CircularProgress size={16} /> : null}
              >
                {refreshing
                  ? "Refreshing..."
                  : hasUnsavedChanges
                  ? "Refresh Preview"
                  : "Refresh"}
              </Button>
            </Box>

            <Box sx={{ height: "calc(100% - 40px)", border: "1px solid #ddd" }}>
              {refreshing ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    flexDirection: "column",
                    gap: 2,
                  }}
                >
                  <CircularProgress />
                  <Typography variant="body2" color="text.secondary">
                    Updating preview...
                  </Typography>
                </Box>
              ) : pdfError ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    flexDirection: "column",
                    gap: 2,
                  }}
                >
                  <Typography color="error" variant="h6">
                    PDF Rendering Error
                  </Typography>
                  <Typography color="error" variant="body2">
                    {pdfError}
                  </Typography>
                  <Button variant="outlined" onClick={handleManualRefresh}>
                    Retry
                  </Button>
                </Box>
              ) : SafePDFComponent ? (
                <PDFViewer
                  key={`pdf-viewer-${selectedTemplate}-${pdfKey}`}
                  style={{ width: "100%", height: "100%" }}
                  showToolbar={true}
                >
                  {SafePDFComponent}
                </PDFViewer>
              ) : (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                  }}
                >
                  <Typography color="error">
                    Template not found or invalid
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CertificatesPage;


const SignatureUpload = ({
  setValue,
  watch,
  name,
  previewName,
  title,
  tournamentId,
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [uploadedUrl, setUploadedUrl] = useState(null);
  const fileRef = useRef(null);

  const signatureFile = watch(name);
  const signaturePreview = watch(previewName);

  const validateSignature = async (file) => {
    if (!file) return false;

    // Validate file type - PNG and JPG allowed
    const allowedTypes = ["image/png", "image/jpeg", "image/jpg"];
    if (!allowedTypes.includes(file.type)) {
      setError("Please upload PNG or JPG image files only");
      return false;
    }

    // Validate file size (max 2MB)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      setError("File size should be less than 2MB");
      return false;
    }

    // Validate image dimensions
    const validateImageDimensions = (file) => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const { width, height } = img;
          // More flexible dimensions for signatures
          if (width > 1000 || height > 400) {
            resolve(false);
          } else {
            resolve(true);
          }
        };
        img.onerror = () => resolve(false);
        img.src = URL.createObjectURL(file);
      });
    };

    const isValidDimensions = await validateImageDimensions(file);
    if (!isValidDimensions) {
      setError("Image dimensions should be maximum 1000x400 pixels");
      return false;
    }

    return true;
  };

  const uploadToS3 = async (file) => {
    const formData = new FormData();
    formData.append("signature", file);
    formData.append("type", name); // chiefArbiterSignature or tournamentDirectorSignature
    formData.append("tournamentId", tournamentId);

    try {
      const response = await Client.post(
        "/tournament/upload-signature",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.lengthComputable) {
              const progress = Math.round(
                (progressEvent.loaded / progressEvent.total) * 100
              );
              setUploadProgress(progress);
            }
          },
          // Optional: Set timeout for large files
          timeout: 30000, // 30 seconds
        }
      );

      return response.data;
    } catch (error) {
      // Handle different types of errors
      if (error.response) {
        // Server responded with error status
        throw new Error(
          error.response.data?.message ||
            `Upload failed with status ${error.response.status}`
        );
      } else if (error.request) {
        // Network error
        throw new Error(
          "Network error during upload. Please check your connection."
        );
      } else {
        // Other errors
        throw new Error(error.message || "Upload failed");
      }
    }
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    setError(null);
    setUploadProgress(0);

    try {
      // Validate file first
      const isValid = await validateSignature(file);
      if (!isValid) {
        setUploading(false);
        return;
      }

      // Create preview URL immediately for better UX
      const previewUrl = URL.createObjectURL(file);
      setValue(previewName, previewUrl);

      // Upload to S3
      const uploadResponse = await uploadToS3(file);

      if (uploadResponse.success) {
        const s3Url = uploadResponse.data.url;

        // Set the S3 URL instead of file object
        setValue(name, s3Url);
        setUploadedUrl(s3Url);

        // Update preview to S3 URL (optional, local preview works too)
        setValue(previewName, s3Url);

        // Clean up local preview URL
        URL.revokeObjectURL(previewUrl);
      } else {
        throw new Error(uploadResponse.message || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setError(error.message || "Failed to upload signature");
      // Clean up on error
      setValue(name, null);
      setValue(previewName, "");
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const removeSignature = async () => {
    // If there's an uploaded URL, optionally delete from S3
    if (uploadedUrl) {
      try {
        await fetch("/api/tournament/delete-signature", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            url: uploadedUrl,
            tournamentId: tournamentId,
          }),
        });
      } catch (error) {
        console.error("Error deleting signature:", error);
      }
    }

    setValue(name, null);
    setValue(previewName, "");
    setUploadedUrl(null);
    setError(null);
    if (fileRef.current) {
      fileRef.current.value = "";
    }
  };

  return (
    <Box sx={{ mb: 3, p: 2, border: "1px solid #e0e0e0", borderRadius: 1 }}>
      <Typography
        variant="subtitle1"
        fontWeight="bold"
        sx={{ fontSize: "1.2rem" }}
      >
        {title}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2, fontSize: "1rem" }}>
          {error}
        </Alert>
      )}

      {uploading && (
        <Box sx={{ mb: 2 }}>
          <LinearProgress
            variant="determinate"
            value={uploadProgress}
            sx={{ mb: 1 }}
          />
          <Typography variant="caption" color="text.secondary">
            Uploading... {uploadProgress}%
          </Typography>
        </Box>
      )}

      <Box
        sx={{ display: "flex", alignItems: "center", gap: 2, flexWrap: "wrap" }}
      >
        {signaturePreview ? (
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            <Avatar
              src={signaturePreview}
              sx={{ width: 80, height: 50, borderRadius: 1 }}
              variant="rounded"
            />
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <Box sx={{ display: "flex", gap: 1 }}>
                <IconButton
                  color="primary"
                  onClick={() => window.open(signaturePreview, "_blank")}
                  size="small"
                  title="View full size"
                >
                  <Visibility />
                </IconButton>
                <IconButton
                  color="error"
                  onClick={removeSignature}
                  size="small"
                  title="Remove signature"
                >
                  <Delete />
                </IconButton>
              </Box>
              {uploadedUrl && (
                <Chip
                  icon={<CheckCircle />}
                  label="Uploaded"
                  color="success"
                  size="small"
                />
              )}
            </Box>
          </Box>
        ) : (
          <Button
            variant="outlined"
            component="label"
            startIcon={<CloudUpload />}
            disabled={uploading}
            sx={{ minWidth: 200 }}
          >
            {uploading ? `Uploading... ${uploadProgress}%` : `Upload ${title}`}
            <input
              type="file"
              hidden
              accept="image/png,image/jpeg,image/jpg"
              ref={fileRef}
              onChange={handleFileSelect}
            />
          </Button>
        )}
      </Box>

      <Typography
        variant="caption"
        color="text.secondary"
        sx={{ mt: 1, display: "block" }}
      >
        PNG or JPG format. Max size: 2MB. Max dimensions: 1000x400 pixels.
      </Typography>
    </Box>
  );
};
