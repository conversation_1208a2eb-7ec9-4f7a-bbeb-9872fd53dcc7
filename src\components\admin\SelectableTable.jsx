import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Checkbox,
  Typography,
  Box,
  Skeleton,
  Divider,
} from "@mui/material";
import { Link } from "react-router-dom";
import CustomPagination from "../common/CustomPagination";


const SelectableTable = ({ 
  data, 
  selectedType, 
  onSelectionChange, 
  loading = false,
  page,
  totalPages,
  onPageChange
}) => {
  const [selectedRows, setSelectedRows] = useState([]);

  // Reset selection when data or selectedType changes
  useEffect(() => {
    setSelectedRows([]);
    onSelectionChange([]);
  }, [data, selectedType]);

  // Memoized function to get unique row ID
  const getRowId = useCallback((row) => {
    if (!row) return '';

    switch (selectedType) {
      case "player":
        return row.cbid || row.id || '';
      case "club":
        return row.clubId || row.id || '';
      case "tournament":
        return row.tournamentId || row.id || '';
      case "arbiter":
        return row.arbiterId || row.id || '';
      default:
        return row.id || row.userId || '';
    }
  }, [selectedType]);

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelectedRows([...data]);
      onSelectionChange([...data]);
    } else {
      setSelectedRows([]);
      onSelectionChange([]);
    }
  };

  const handleSelectRow = useCallback((row) => {
    if (!row) return;

    const rowId = getRowId(row);
    const isSelected = selectedRows.some(selected => getRowId(selected) === rowId);
    let newSelection;

    if (isSelected) {
      newSelection = selectedRows.filter(selected => getRowId(selected) !== rowId);
    } else {
      newSelection = [...selectedRows, row];
    }

    setSelectedRows(newSelection);
    onSelectionChange(newSelection);
  }, [selectedRows, getRowId, onSelectionChange]);

  const isRowSelected = useCallback((row) => {
    if (!row) return false;
    const rowId = getRowId(row);
    return selectedRows.some(selected => getRowId(selected) === rowId);
  }, [selectedRows, getRowId]);

  const getColumns = useCallback(() => {
    const baseColumns = [
      {
        id: "index",
        label: "S No",
        width: "80px",
        format: (_, item, index) => index + 1,
      },
    ];

    switch (selectedType) {
      case "player":
        return [
          ...baseColumns,
          { id: "playerTitle", label: "Title", width: "80px" },
          {
            id: "name",
            label: "Player Name",
            format: (_, item) => (
              <Link 
                to={`/players/${item?.cbid || ''}`} 
                style={{ textDecoration: "none", color: "inherit" }}
                onClick={(e) => e.stopPropagation()}
              >
                {item?.name || item?.playerName || '-'}
              </Link>
            ),
          },
          { id: "cbid", label: "CBID" },
          { id: "fideId", label: "FIDE ID" },
          { id: "aicfId", label: "AICF ID" },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
        ];
      case "club":
        return [
          ...baseColumns,
          {
            id: "clubName",
            label: "Club Name",
            format: (_, item) => (
              <Link 
                to={`/clubs/${item?.clubId || ''}`} 
                style={{ textDecoration: "none", color: "inherit" }}
                onClick={(e) => e.stopPropagation()}
              >
                {item?.clubName || '-'}
              </Link>
            ),
          },
          { id: "clubId", label: "Club ID" },
          { id: "email", label: "Email" },
          { 
            id: "phone", 
            label: "Phone",
            format: (_, item) => item?.phone || item?.phoneNumber || item?.mobile || '-'
          },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
          { id: "city", label: "City" },
        ];
      case "arbiter":
        return [
          ...baseColumns,
          {
            id: "name",
            label: "Arbiter Name",
            format: (_, item) => (
              <Link 
                to={`/arbiters/${item?.id || item?.arbiterId || ''}`} 
                style={{ textDecoration: "none", color: "inherit" }}
                onClick={(e) => e.stopPropagation()}
              >
                {item?.name || '-'}
              </Link>
            ),
          },
          { id: "arbiterId", label: "Arbiter ID" },
          { id: "email", label: "Email" },
          { 
            id: "phone", 
            label: "Phone",
            format: (_, item) => item?.phone || item?.phoneNumber || item?.mobile || '-'
          },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
          { id: "city", label: "City" },
        ];
      case "tournament":
        return [
          ...baseColumns,
          {
            id: "title",
            label: "Tournament Name",
            format: (_, item) => (
              <Link 
                to={`/tournaments/${encodeURIComponent(item?.title || '')}`} 
                style={{ textDecoration: "none", color: "inherit" }}
                onClick={(e) => e.stopPropagation()}
              >
                {item?.title || '-'}
              </Link>
            ),
          },
          { id: "tournamentId", label: "Tournament ID" },
          {
            id: "startDate",
            label: "Start Date",
            format: (value) => {
              if (!value) return "-";
              try {
                return new Date(value).toLocaleDateString();
              } catch (error) {
                return "-";
              }
            }
          },
          {
            id: "endDate",
            label: "End Date",
            format: (value) => {
              if (!value) return "-";
              try {
                return new Date(value).toLocaleDateString();
              } catch (error) {
                return "-";
              }
            }
          },
          { id: "country", label: "Country" },
          { id: "state", label: "State" },
          { id: "district", label: "District" },
          { id: "city", label: "City" },
        ];
      default:
        return [
          ...baseColumns,
          { id: "name", label: "Name" },
          { id: "userId", label: "User ID" },
          { id: "email", label: "Email" },
          { 
            id: "phone", 
            label: "Phone",
            format: (_, item) => item?.phone || item?.phoneNumber || item?.mobile || '-'
          },
          { id: "role", label: "Role" },
        ];
    }
  }, [selectedType]);

  // Function to render cell content based on column definition
  const renderCellContent = useCallback((item, column, index) => {
    if (!item) return "-";

    const value = item[column.id];

    // If column has a custom format function, use it
    if (column.format) {
      try {
        return column.format(value, item, index);
      } catch (error) {
        console.error(`Error formatting column ${column.id}:`, error);
        return "-";
      }
    }

    // Default formatting based on value type
    if (value === null || value === undefined || value === "") {
      return "-";
    }

    return String(value);
  }, []);

  const columns = getColumns();
  const isAllSelected = data.length > 0 && selectedRows.length === data.length;
  const isIndeterminate = selectedRows.length > 0 && selectedRows.length < data.length;

  // Loading state
  if (loading) {
    return (
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: "#CCBEF033" }}>
                <TableCell padding="checkbox">
                  <Skeleton variant="rectangular" width={24} height={24} />
                </TableCell>
                {columns.map((column) => (
                  <TableCell key={column.id}>
                    <Skeleton variant="text" width="80%" />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  <TableCell padding="checkbox">
                    <Skeleton variant="rectangular" width={24} height={24} />
                  </TableCell>
                  {columns.map((column) => (
                    <TableCell key={`skeleton-${index}-${column.id}`}>
                      <Skeleton variant="text" width="60%" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  // No data state
  if (!data || data.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: "center" }}>
        <Typography variant="h6" color="text.secondary">
          No results found. Please refine your search criteria.
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer sx={{ maxHeight: 600, overflowX: "auto" }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow
              sx={{
                ".MuiTableCell-root": { 
                  textWrap: "nowrap",
                  backgroundColor: "#CCBEF0",
                },
              }}
            >
              <TableCell padding="checkbox" sx={{ backgroundColor: "#CCBEF0" }}>
                <Checkbox
                  indeterminate={isIndeterminate}
                  checked={isAllSelected}
                  onChange={handleSelectAll}
                  color="primary"
                  inputProps={{ 'aria-label': 'select all rows' }}
                />
              </TableCell>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || "left"}
                  sx={{ 
                    ...(column.width ? { width: column.width } : {}),
                    fontWeight: "bold",
                    backgroundColor: "#CCBEF033",
                  }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, index) => {
              const rowId = getRowId(row);
              const isSelected = isRowSelected(row);

              // Skip rows without valid ID
              if (!rowId) {
                console.warn(`Row at index ${index} has no valid ID:`, row);
                return null;
              }

              return (
                <React.Fragment key={rowId}>
                  <TableRow
                    hover
                    onClick={() => handleSelectRow(row)}
                    role="checkbox"
                    aria-checked={isSelected}
                    selected={isSelected}
                    sx={{
                      cursor: "pointer",
                      "&:nth-of-type(odd)": { backgroundColor: "#BEDDF026" },
                      "&:nth-of-type(even)": { backgroundColor: "#DAECF81F" },
                      "&.Mui-selected": {
                        backgroundColor: "#e3f2fd !important",
                      },
                      "&:hover": {
                        backgroundColor: "#f5f5f5 !important",
                      },
                      ".MuiTableCell-root": { textWrap: "nowrap" },
                    }}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={isSelected}
                        color="primary"
                        onChange={() => handleSelectRow(row)}
                        onClick={(e) => e.stopPropagation()}
                        inputProps={{ 'aria-label': `select row ${index + 1}` }}
                      />
                    </TableCell>
                    {columns.map((column) => (
                      <TableCell
                        key={`${rowId}-${column.id}`}
                        align={column.align || "left"}
                        sx={column.width ? { width: column.width } : {}}
                      >
                        {renderCellContent(row, column, index)}
                      </TableCell>
                    ))}
                  </TableRow>

                  {/* Divider between rows - only show if not the last row */}
                  {index < data.length - 1 && (
                    <TableRow key={`divider-${rowId}`}>
                      <TableCell
                        colSpan={columns.length + 1}
                        sx={{ p: 0, borderBottom: "none" }}
                      >
                        <Divider />
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Selection Summary */}
      <Box sx={{ 
        p: 2, 
        bgcolor: "#f5f5f5", 
        borderTop: "1px solid #e0e0e0",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <Typography variant="body2" color="text.primary">
          {selectedRows.length} of {data.length} items selected
        </Typography>

        {/* Pagination info if provided */}
        {page && totalPages && (
        <CustomPagination
          totalPages={totalPages}
          currentPage={page}
          onPageChange={onPageChange}
        />
        )}
      </Box>
    </Paper>
  );
};

export default SelectableTable;