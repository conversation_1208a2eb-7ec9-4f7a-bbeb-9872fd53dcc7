import React from "react";
import {
  TextField,
  Typography,
  Box,
  FormHelperText,
} from "@mui/material";
import { Controller } from "react-hook-form";
import { RestartAlt } from "@mui/icons-material";

/**
 * FormDatePicker - A date picker component for React Hook Form
 * 
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {Object} props.control - React Hook Form control object
 * @param {string} props.title - Label for the date picker
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Whether the field is required
 * @param {Object} props.rules - Additional validation rules
 * @param {string} props.defaultValue - Default date value (format: "YYYY-MM-DD")
 * @param {Object} props.sx - Additional styles for the component
 * @param {boolean} props.minToday - Whether to set the minimum date to today
 */
const FormDatePicker = ({
  name,
  control,
  title,
  placeholder,
  required = false,
  rules = {},
  defaultValue = "",
  sx = {},
  minToday = false,
  ...rest
}) => {
  // Combine required rule with other rules
  const validationRules = required
    ? { required: `${title} is required`, ...rules }
    : rules;

  return (
    <Box sx={{ mb: 2, ...sx }}>
      {title && (
        <Typography
          variant="subtitle1"
          sx={{
            fontWeight: required ? 500 : 400,
            // mb: 0.5,
            fontSize: 14,
            color: "text.primary",
            textAlign: "start",
            "&::after": required
              ? {
                  content: '" *"',
                  color: "error.main",
                }
              : {},
          }}
        >
          {title}
        </Typography>
      )}

      <Controller
        name={name}
        control={control}
        rules={validationRules}
        defaultValue={defaultValue}
        render={({ field, fieldState: { error } }) => (
          <>
            <TextField
              {...field}
              type="date"
              fullWidth
              placeholder={placeholder}
              size="small"
              inputProps={{
                min: minToday ? new Date().toISOString().split("T")[0] : undefined,
                ...rest.inputProps,
              }}
              error={!!error}
              InputLabelProps={{ shrink: true }}
            />
            {error && (
              <FormHelperText error sx={{ ml: 1.5 }}>
                {error.message}
              </FormHelperText>
            )}
          </>
        )}
      />
    </Box>
  );
};

export default FormDatePicker;
