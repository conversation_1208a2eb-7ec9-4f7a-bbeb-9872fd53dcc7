import React, { useState, useEffect } from 'react';
import {
  Box, Button, Dialog, DialogTitle, DialogContent, DialogActions,
  Typography, Avatar, TextField, IconButton, Alert, Stack, CircularProgress
} from '@mui/material';
import {
  Close as X,
  Security as Shield,
} from '@mui/icons-material';
import axios from 'axios';
import UseGlobalContext from '../../lib/hooks/UseGlobalContext';
import { Client } from "../../api/client";
import UseToast from '../../lib/hooks/UseToast';

const AdminBanVerificationPopup = ({ User, open, onClose, isActive, onSuccess }) => {
  const [step, setStep] = useState('confirm');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { user } = UseGlobalContext();
  const toast = UseToast()
  const [email, setEmail] = useState(user?.email || '');
  const [timer, setTimer] = useState(60);
  const [isTimerActive, setIsTimerActive] = useState(false);

  // console.log("isActive", isActive)
  // console.log("onsuccess", onSuccess)

  // Timer effect
  useEffect(() => {
    let interval = null;
    
    if (isTimerActive && timer > 0) {
      interval = setInterval(() => {
        setTimer(timer => timer - 1);
      }, 1000);
    } else if (timer === 0) {
      setIsTimerActive(false);
      // Optionally reset timer or handle timeout
      setTimer(60);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isTimerActive, timer]);

  console.log("step",step)
  console.log("isTimerActive",isTimerActive)

  // API: Generate OTP
  const handleConfirmBan = async () => {
    setLoading(true);
    setError('');
    setIsTimerActive(true); // Start the timer
    
    const data = {
      email: user.email,
      userId: user.userId,
      context: 'ban'
    }
    
    try {
      const response = await Client.post('/admin/access/get-otp', {
        ...data
      });
      if (response.data.success) {
        setStep('otp');
        toast.success("otp send successfully")
      } else {
        setError('Failed to generate OTP');
        toast.error('Failed to generate OTP')
        setIsTimerActive(false); // Stop timer on error
      }
    } catch (err) {
      setError('Error generating OTP');
      toast.error('Error generating OTP');
      setIsTimerActive(false); // Stop timer on error
    } finally {
      setLoading(false);
    }
  };

  const handleOtpChange = (index, value) => {
    if (value.length > 1) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    }
  };

  const handleKeyPress = (e, index) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  // Function to Ban User
  const banUser = async () => {
    try {
      const banResponse = await Client.post('/admin/access/ban', {
        userId: User.id,
        value: !isActive,
      });

      if (banResponse.data.success) {
        setStep('success');
        toast.success('user Banned successfully');
        setIsTimerActive(false); // Stop timer on success
        setTimeout(() => {
          handleClosePopup();
          onSuccess();  // Ensure this is a function
        }, 1000);
      } else {
        setError('Failed to ban user.');
      }
    } catch (err) {
      setError('Error banning user.');
      toast.error('Error banning user.');
    }
  };

  // Verify OTP and then call `banUser` if successful
  const handleVerifyOtp = async () => {
    const enteredOtp = otp.join('');
    if (enteredOtp.length !== 6) return setError('Enter complete OTP');

    setLoading(true);
    setError('');

    try {
      const verifyResponse = await Client.post('/admin/access/otp-verify', {
        email: user.email,
        userId: user.userId,
        otp: enteredOtp,
      });

      if (verifyResponse.data.success) {
        toast.success('otp verified successfully');
        banUser();
      } else {
        toast.error(verifyResponse.data.error);
      }
    } catch (err) {
      setError('Error verifying OTP');
      toast.error('Error verifying OTP');
    } finally {
      setLoading(false);
    }
  };

  const handleClosePopup = () => {
    setStep('confirm');
    setOtp(['', '', '', '', '', '']);
    setError('');
    setTimer(60);
    setIsTimerActive(false);
    if(isTimerActive){
      setStep('otp');
    }
    onClose();
  };

  // Function to resend OTP
  const handleResendOtp = async () => {
    setTimer(60);
    setIsTimerActive(true);
    await handleConfirmBan(); // Reuse the same function to send OTP
  };

  return (
    <Dialog open={open} onClose={handleClosePopup} fullWidth maxWidth="sm">
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" spacing={1} alignItems="center">
            <Shield color={step === 'otp' ? 'blue' : 'red'} size={20} />
            <Typography variant="h6">
              {step === 'confirm' ? 'Confirm User Ban' : step === 'otp' ? 'Verify OTP' : 'Success'}
            </Typography>
          </Stack>
          <IconButton onClick={handleClosePopup} size="small">
            <X size={20} />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent dividers>
        {step === 'confirm' && (
          <>
            <Alert severity="warning" sx={{ mb: 2, fontSize: '18px !important' }}>
              This action will permanently ban the user from the platform.
            </Alert>
            <Stack direction="row" spacing={2} alignItems="center">
              <Box>
                <Typography fontWeight={500}>Name: {User.name}</Typography>
                <Typography variant="body2" color="#000">Mail: {User.email}</Typography>
              </Box>
            </Stack>
          </>
        )}
        {step === 'otp' && (
          <>
            <Typography variant="body2" color="#000" mb={2}>
              Enter the 6-digit OTP sent to your registered admin Phone Number.
            </Typography>
            <Stack direction="row" spacing={1} justifyContent="center" mb={2}>
              {otp.map((digit, i) => (
                <TextField
                  key={i}
                  id={`otp-${i}`}
                  inputProps={{ maxLength: 1, style: { textAlign: 'center' } }}
                  value={digit}
                  onChange={(e) => handleOtpChange(i, e.target.value)}
                  onKeyDown={(e) => handleKeyPress(e, i)}
                  variant="outlined"
                  size="small"
                  sx={{ width: 40 }}
                />
              ))}
            </Stack>
            
            {/* Timer and Resend OTP section */}
            <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="body2" color="text.secondary">
                {isTimerActive ? `Resend OTP in ${timer}s` : 'Didn\'t receive OTP?'}
              </Typography>
              {!isTimerActive && (
                <Button 
                  variant="text" 
                  size="small" 
                  onClick={handleResendOtp}
                  disabled={loading}
                >
                  Resend OTP
                </Button>
              )}
            </Stack>
          </>
        )}
        {step === 'success' && (
          <Alert severity="success">User has been successfully banned!</Alert>
        )}
      </DialogContent>
      {step !== 'success' && (
        <DialogActions>
          <Button onClick={handleClosePopup}>Cancel</Button>
          <Button
            onClick={step === 'confirm' ? handleConfirmBan : handleVerifyOtp}
            variant="contained"
            color={step === 'confirm' ? 'error' : 'primary'}
            disabled={loading}
          >
            {loading ? (
              <CircularProgress size={24} />
            ) : step === 'confirm' ? (
              `Proceed to Verify ${isTimerActive ? `(${timer}s)` : ''}`
            ) : (
              'Verify OTP'
            )}
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default AdminBanVerificationPopup;