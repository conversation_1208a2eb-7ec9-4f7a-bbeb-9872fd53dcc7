import React, { useState, useMemo } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  Alert,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const CANCELLATION_REASONS = [
  { value: "personal_reasons", label: "Personal Reasons" },
  { value: "scheduling_conflict", label: "Scheduling Conflict" },
  { value: "health_issues", label: "Health Issues" },
  { value: "other", label: "Other" },
];

const CancellationModal = ({
  open,
  onClose,
  tournament,
  onCancellationSuccess,
  onsuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [cancellationData, setCancellationData] = useState({
    reason: "",
    comments: "",
  });
  const [formErrors, setFormErrors] = useState({});
  const toast = UseToast();

  // Memoized refund calculation
  const refundInfo = useMemo(() => {
    if (!tournament?.startDate)
      return {
        type: "unknown",
        message: "Refund eligibility will be determined.",
      };

    const tournamentStartDate = new Date(tournament.startDate);
    const now = new Date();
    const diffInHours = (tournamentStartDate - now) / (1000 * 60 * 60);

    if (diffInHours < 0) {
      return {
        type: "none",
        message: "No refund available as the tournament has already started.",
        severity: "error",
      };
    } else if (diffInHours >= 48) {
      return {
        type: "full",
        message: "You will receive a full refund for this cancellation.",
        severity: "success",
      };
    } else {
      return {
        type: "partial",
        message:
          "You will receive a partial refund as it's less than 48 hours before the tournament.",
        severity: "warning",
      };
    }
  }, [tournament?.startDate]);

  const validateForm = () => {
    const errors = {};
    if (!cancellationData.reason.trim()) {
      errors.reason = "Please select a reason for cancellation";
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCancellationData((prev) => ({ ...prev, [name]: value }));

    // Clear field-specific error
    if (formErrors[name]) {
      setFormErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);

    try {
      const response = await Client.post(`/payment/refund/initiate`, {
        tournamentId: tournament.id,
        reason: cancellationData.reason,
        comments: cancellationData.comments,
      });

      if (response.data.success) {
        toast.success("Registration cancelled successfully");
        onCancellationSuccess?.(response.data.data);
        onsuccess();
        onClose();
      } else {
        setError(response.data.message || "Failed to cancel registration");
      }
    } catch (err) {
      console.error("Cancellation error:", err);
      setError(
        err.response?.data?.message ||
          err.response?.data?.error ||
          "An error occurred while cancelling your registration"
      );
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount, currency = "INR") =>
    `${currency} ${parseFloat(amount || 0).toFixed(2)}`;

  const formatDate = (dateString) =>
    dateString ? new Date(dateString).toLocaleDateString() : "N/A";

  const formatTournamentTitle = (title) => title?.replace(/-/g, " ") || "N/A";

  return (
    <Dialog
      open={open}
      onClose={loading ? undefined : onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2, boxShadow: 3 },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: "#f5f5f5",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          py: 2,
        }}
      >
        <Typography variant="h6" component="div" fontWeight="bold">
          Cancel Registration
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          disabled={loading}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3, fontSize: "1rem" }}>
            {error}
          </Alert>
        )}

        {/* Tournament Details */}
        <Box sx={{ mb: 3 }}>
          <Typography
            variant="subtitle1"
            fontWeight="medium"
            sx={{ fontSize: "1.5rem", mb: 2 }}
          >
            Tournament Details
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 1,
              bgcolor: "#f9f9f9",
              p: 2,
              borderRadius: 1,
            }}
          >
            <Typography variant="body2" sx={{ fontSize: "1.2rem" }}>
              <strong>Tournament:</strong>{" "}
              {formatTournamentTitle(tournament?.title)}
            </Typography>
            <Typography variant="body2" sx={{ fontSize: "1.2rem" }}>
              <strong>Start Date:</strong> {formatDate(tournament?.startDate)}
            </Typography>
            <Typography variant="body2" sx={{ fontSize: "1.2rem" }}>
              <strong>Entry Fee:</strong>{" "}
              {formatCurrency(
                tournament?.entryFee,
                tournament?.entryFeeCurrency
              )}
            </Typography>
            <Typography variant="body2" sx={{ fontSize: "1.2rem" }}>
              <strong>Location:</strong> {tournament?.venueAddress || "N/A"}
            </Typography>
          </Box>
        </Box>

        {/* Refund Information */}
        <Alert severity={refundInfo.severity} sx={{ mb: 2, fontSize: "1rem" }}>
          {refundInfo.message}
        </Alert>

        <Divider sx={{ mb: 2 }} />

        {/* Cancellation Form */}
        <FormControl fullWidth margin="normal" error={!!formErrors.reason}>
          <InputLabel id="reason-label">Reason for Cancellation</InputLabel>
          <Select
            labelId="reason-label"
            name="reason"
            value={cancellationData.reason}
            onChange={handleInputChange}
            label="Reason for Cancellation"
            disabled={loading}
            sx={{ fontSize: "1rem" }}
          >
            {CANCELLATION_REASONS.map(({ value, label }) => (
              <MenuItem key={value} value={value}>
                {label}
              </MenuItem>
            ))}
          </Select>
          {formErrors.reason && (
            <FormHelperText error>{formErrors.reason}</FormHelperText>
          )}
        </FormControl>

        <TextField
          fullWidth
          margin="normal"
          placeholder="Additional Comments (Optional)"
          name="comments"
          value={cancellationData.comments}
          onChange={handleInputChange}
          multiline
          rows={3}
          disabled={loading}
        />
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, bgcolor: "#f5f5f5" }}>
        <Button
          onClick={onClose}
          color="inherit"
          disabled={loading}
          sx={{ fontWeight: "medium", fontSize: "1rem" }}
        >
          Keep Registration
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={loading || refundInfo.type === "none"}
          startIcon={loading && <CircularProgress size={20} color="inherit" />}
          sx={{ fontWeight: "medium", fontSize: "1rem" }}
        >
          {loading ? "Processing..." : "Cancel Registration"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CancellationModal;
