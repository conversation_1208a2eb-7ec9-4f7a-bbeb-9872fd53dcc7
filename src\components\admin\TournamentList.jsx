import React, { useEffect, useState } from 'react';
import {
  <PERSON>, Typo<PERSON>, Button, CircularProgress, Box, TextField,
  MenuItem,
  Select,
  Chip,
} from '@mui/material';
import { Client } from '../../api/client';
import { useNavigate } from 'react-router-dom';
import { RestartAlt, Search as SearchIcon, AccountBalance as PayoutIcon } from "@mui/icons-material";
import DynamicTable from '../common/DynamicTable';


const TournamentList = () => {
  const [tournaments, setTournaments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPage, setTotalPage] = useState(0);
  const [search, setSearch] = useState({
    tournamentName: '',
    date: '7'
  })
  const navigate = useNavigate();

  const fetchTournaments = async (page) => {
    try {
      setLoading(true);
      let whereClause = {}

      if (search.tournamentName) {
        whereClause.tournamentName = search.tournamentName
      }

      if (search.date) {
        whereClause.date = search.date
      }

      const res = await Client.get(`/admin/details/payment/tournament`, {
        params: {
          page,
          ...whereClause
        }
      });
      if (res.data.success) {
        setTournaments(res.data.data || []);
        setTotalPage(res.data.pagination.totalPages || 0);
      }
    } catch (error) {
      console.error('Error fetching tournaments:', error);
      setTournaments([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTournaments(page);
  }, []);

  const handlePageChange = (newPage) => {
    setPage(newPage);
    fetchTournaments(newPage);
  };

  const handleSearch = () => {
    setPage(1);
    fetchTournaments(1);
  };

  const handleViewDetails = (title) => {
    navigate(`/dashboard/payments/details/${title}`);
  };

  function formatTitle(title) {
    return title.replace(/-+$/, "").replace(/-/g, " ");
  }

  const handleReset = () => {
    setSearch({ tournamentName: '', date: "7" })
    setPage(1);
    fetchTournaments(1);
  }
  return (
    <Box padding={3}>
      <Box mb={2}> <Typography variant="h4" fontWeight={500} color="#000">
        Tournaments Lists
      </Typography></Box>

      {/* 🔍 Search Filters */}
      <Card sx={{ padding: 2, mb: 3 }}>

        <Typography variant="h4" color='Darkblue' gutterBottom>
          Search Tournament List
        </Typography>
        <Box display="flex" gap={2} mb={3} alignItems={'end'}>
          <TextField
            variant="outlined"
            fullWidth
            value={search.tournamentName}
            placeholder='Enter Tournament Title'
            onChange={(e) => setSearch(prev => ({ ...prev, tournamentName: e.target.value }))}
          />
          <Select
            variant="outlined"
            fullWidth
            value={search.date}
            onChange={(e) => setSearch(prev => ({ ...prev, date: e.target.value }))}
          >
            <MenuItem value={'7'}>Past 7 days</MenuItem>
            <MenuItem value={'30'}>Past 30 days</MenuItem>
            <MenuItem value={'all'}>All</MenuItem>
          </Select>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="containedSecondary"
              color="secondary"
              sx={{
                width: "40px",
                minWidth: "40px !important",
              }}
              onClick={handleReset}
              disabled={loading}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              disabled={loading}
              startIcon={
                loading ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <SearchIcon />
                )
              }
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "40px",
                fontSize: "16px",
                maxWidth: {
                  xs: "100%",
                  sm: "150px",
                },
              }}
            >
              Search
            </Button>
          </Box>
        </Box>
      </Card>
      <Box display="flex" flexDirection="column" gap={2}>
        {/* {tournaments.map((tournament) => (
            <Card key={tournament.id}>
              <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box>
                  <Typography variant="h6">
                    <strong>Title:</strong> {tournament.title ? formatTitle(tournament.title) : '-'}
                  </Typography>
                </Box>
                <Button
                  sx={{ minWidth: '150px' }}
                  variant="outlined"
                  onClick={() => handleViewDetails(tournament.title)}
                >
                  View Detail
                </Button>
              </CardContent>
            </Card>
          ))} */}  

        <DynamicTable
          columns={[

            {
              id: "no",
              label: "S.no",
              format: (tournament, _, index) => (
                <Typography variant="h6" sx={{ fontWeight: "medium", fontSize: "16px" }}>
                  {(page - 1) * 10 + index + 1}
                </Typography>
              )
            },
            {
              id: "title",
              label: "Tournament Name",
              format: (_, tournament) => (

                <Typography
                  variant="h6"
                  sx={{ fontWeight: "medium", fontSize: "16px" }}
                >
                  {tournament.title ? formatTitle(tournament.title) : '-'}
                </Typography>
              ),
            },
              {
              id: "totalPaymentAmount",
              label: "Total Amount",
              format: (_, tournament) => (
                tournament.totalPaymentAmount ? (
                <Typography
                  variant="h6"
                  sx={{ fontWeight: "medium", fontSize: "16px" }}
                >
                  ₹{tournament.totalPaymentAmount}
                </Typography>):'-'
              ),
            },
            {
              id: "payoutStatus",
              label: "Payout Status",
              format: (_, tournament) => {
                // You can add payout status logic here based on tournament data
                const hasPayouts = tournament.payoutStatus || false;
                return (
                  <Chip
                    label={hasPayouts ? "Processed" : "Pending"}
                    color={hasPayouts ? "success" : "warning"}
                    size="small"
                    variant="outlined"
                  />
                );
              },
            },
            {
              id: "actions",
              label: "Actions",
              format: (_, tournament) => (
                <Box display="flex" gap={1}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<PayoutIcon />}
                    onClick={() => navigate(`/dashboard/payments/details/${tournament.title}`)}
                    sx={{ minWidth: "120px" }}
                  >
                    Manage Payout
                  </Button>
                </Box>
              ),
            },

          ]}
          data={tournaments}
          loading={loading}
          page={page}
          totalPages={totalPage}
          onPageChange={handlePageChange}
          detailsPath="/dashboard/payments/details/"
          idField="title"
          showDetailsButton={true}

          tableContainerProps={{
            sx: {
              minHeight: "200px",
              maxHeight: "400px",
            },
          }}
        />
      </Box>
    </Box>
  );
};

export default TournamentList;