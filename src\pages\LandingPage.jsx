import ArrowForwardIcon from "@mui/icons-material/ArrowForward";

import {
  <PERSON>,
  But<PERSON>,
  Container,
  Grid,
  Typography,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import React, { useEffect } from "react";

// import HeroBackgroundImage from "../assets/images/homepageBackgroundchess.png";
import HeroBackgroundImage from "../assets/images/crop.png";
import CorporateStrategy from "../assets/images/corporate-strategy.png";
import Calendar from "../assets/images/calendar.png";
import ComputerChess from "../assets/images/computerchess.png";
import { Link, useNavigate } from "react-router-dom";

import useGlobalContext from "../lib/hooks/UseGlobalContext";

const features = [
  {
    title: "Puzzles",
    icon: CorporateStrategy,
    description: "Practice with chess puzzles and improve your game",
    link: "#",
  },
  {
    title: "Register for Tournaments",
    icon: Calendar,
    description: "Find and register for chess tournaments near you",
    link: "#",
  },
  {
    title: "Play Online",
    icon: ComputerChess,
    description: "Challenge players from around the world in online matches",
    link: "#",
  },
];

const LandingPage = () => {
  const { isLoggedIn } = useGlobalContext();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  useEffect(() => {
    if (isLoggedIn) {
      navigate("/dashboard");
    }
  }, []);

  return (
    <Box
      sx={{
        bgcolor: "background.paper",
        overflowX: "hidden",
        minHeight: {
          xs: "50vh",
          sm: "80vh",
          md: "130vh",
          lg: "130vh",
          xl: "130vh",
        }, // do some change in minheight for option remove
      }}
    >
      <Box
        sx={{
          bgcolor: "white",
          minHeight: {
            xs: "50vh",
            sm: "80vh",
            md: "120vh",
            lg: "130vh",
            xl: "130vh", // do some change in minheight for option remove
          },
          display: "flex",
          flexDirection: "column",
          overflow: "hidden",
        }}
        className="jagadeesh"
      >
        <Box
          sx={{
            position: "relative",
            textAlign: "center",
            pb: 4,
          }}
        >
          <Box
            sx={{
              position: "relative",
              zIndex: 5,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              direction: "column",
              py: 8,
            }}
          >
            <Box
              component="img"
              src={HeroBackgroundImage}
              alt="C hess pieces"
              sx={{
                position: "absolute",
                width: "100%",
                height: "auto",
                top: {
                  xs: 100,
                  sm: 80,
                  md: 80,
                  lg: 60,
                  xl: 80,
                },
                left: 0,
                right: 0,
                transform: "rotate(0.55deg)",
                margin: "0 auto",
                zIndex: 4,
              }}
            />
            <Box
              component="svg"
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="xMidYMid meet"
              viewBox="0 0 900 414"
              sx={{
                position: "absolute",
                top: 0,
                margin: "0 auto",
                width: {
                  xs: "150vw",
                  sm: "120vw",
                  md: "110vw",
                  lg: "100vw",
                  xl: "100%",
                },
                height: "auto",

                zIndex: -1,
              }}
            >
              <path
                d="M900 0.5V281.771L462.5 413.5L0 293.51V0.5H900Z"
                fill="url(#paint0_linear_220_1129)"
                // fillOpacity="1"
                fillOpacity="0.23"
              />
              <defs>
                <linearGradient
                  xmlns="http://www.w3.org/2000/svg"
                  id="paint0_linear_220_1129"
                  x1="900"
                  y1="177.331"
                  x2="-0.000375146"
                  y2="167.983"
                  gradientUnits="userSpaceOnUse"
                >
                  <stop stop-color="#9F9F9F" />
                  <stop offset="0.333333" stop-color="white" />
                  <stop offset="0.666667" stop-color="white" />
                  <stop offset="1" stop-color="#9F9F9F" />
                </linearGradient>
              </defs>
            </Box>
            <Container
              maxWidth="lg"
              sx={{
                position: "relative",
                zIndex: 5,
                pt: { xs: 0, sm: 4, md: 6, lg: 8, xl: 12 },
                transition: "all 0.3s ease-in-out",
              }}
            >
              <Typography
                variant={isMobile ? "h4" : "h3"}
                component="h1"
                sx={{
                  fontWeight: "regular",
                  mb: { xs: 1, sm: 2, md: 3 },
                  fontSize: {
                    xs: "25px",
                    sm: "30px",
                    md: "40px",
                    lg: "42px",
                    xl: "46px",
                  },
                }}
              >
                ChessBrigade.com
              </Typography>

              <Typography
                variant={isMobile ? "body1" : "h5"}
                sx={{
                  mb: 4,
                  fontSize: {
                    xs: "12px",
                    sm: "14px",
                    md: "20px",
                    lg: "22px",
                    xl: "24px",
                  },
                  fontWeight: "regular",
                }}
              >
                Register for Tournaments / Solve Puzzles / Practice Online &
                Many more
              </Typography>
            </Container>
          </Box>
        </Box>
      </Box>
      {/* <Box
        sx={{
          py: 8,
          bgcolor: "transparent",
          position: "relative",
          mt: { xs: 0, sm: 5, md: 5, lg: 5, xl: 5 },
          mb: 10,
          zIndex: 6,
          pt: { xs: 10, sm: 10, md: 10, lg: 20, xl: 30 },
        }}
      >
        <Container maxWidth="lg" sx={{ pt: 5 }}>
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid
                item
                xs={12}
                md={4}
                key={index}
                sx={{ textAlign: "center" }}
              >
                <Box
                  component="img"
                  src={feature.icon}
                  alt={feature.title}
                  sx={{
                    width: { xs: 40, sm: 60, md: 80 },
                    height: { xs: 40, sm: 60, md: 80 },
                    mb: 2,
                  }}
                />

                <Typography
                  variant="h5"
                  component="h3"
                  sx={{
                    fontWeight: "bold",
                    mb: { xs: 1, sm: 2, md: 3 },
                    fontSize: { xs: 16, sm: 18, md: 20 },
                  }}
                >
                  {feature.title}
                </Typography>

                <Typography
                  variant="h6"
                  sx={{
                    mb: 2,
                    fontSize: { xs: 14, sm: 16, md: 18 },
                    px: { xs: "50px", sm: "30px", md: "0px" },
                    textAlign: "center",
                  }}
                >
                  {feature.description}
                </Typography>

                <Link to={feature.link}>
                  <Typography
                    sx={{
                      display: "inline-flex",
                      alignItems: "center",
                      justifyContent: "center",
                      color: "#4CAF50",
                      fontWeight: "medium",
                      fontSize: { xs: 14, sm: 16, md: 18 },
                      textDecoration: "none",
                      ":hover": {
                        textDecoration: "underline",
                      },
                    }}
                  >
                    Learn more
                    <ArrowForwardIcon
                      sx={{ ml: 1, fontSize: { xs: 12, sm: 14, md: 16 } }}
                    />
                  </Typography>
                </Link>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box> */}
    </Box>
  );
};

export default LandingPage;
