import React from 'react';
import { <PERSON>, Typo<PERSON>, <PERSON>ack, Card, CardContent, Skeleton, Button, Pagination } from '@mui/material';

const AddSkeleton = () => {
  const skeletonArray = Array.from({ length: 3 }); // simulate 3 loading cards

  return (
    <Box p={2}>
      <Typography
        variant="h4"
        mb={2}
        gutterBottom
        sx={{ fontWeight: 'bold', color: 'primary.main' }}
      >
        Previous Uploads
      </Typography>

      <Stack spacing={2}>
        {skeletonArray.map((_, index) => (
          <Card key={index}>
            <CardContent sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Box>
                <Skeleton variant="text" width={200} height={30} />
                <Skeleton variant="text" width={180} height={30} />
                <Skeleton variant="text" width={160} height={30} />
                <Skeleton variant="text" width={220} height={30} />
              </Box>
              <Box mt={1} display={'flex'} gap={5}>
                <Skeleton variant="rectangular" width={100} height={36} sx={{ borderRadius: 1 }} />
                <Skeleton variant="rectangular" width={80} height={36} sx={{ ml: 2, borderRadius: 1 }} />
              </Box>
            </CardContent>
          </Card>
        ))}

        <Box mt={3} sx={{ display: 'flex', justifyContent: 'center' }}>
          <Skeleton variant="rectangular" width={200} height={40} sx={{ borderRadius: 2 }} />
        </Box>
      </Stack>
    </Box>
  );
};

export default AddSkeleton;