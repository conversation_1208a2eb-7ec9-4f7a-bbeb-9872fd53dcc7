import React from "react";
import HeroBackgroundImage from "../assets/images/crop.png";
import { Box, Skeleton, Typography } from "@mui/material";
import { useParams } from "react-router-dom";
import { Client } from "../api/client";
import { useState, useEffect } from "react";
import { chessPolicies } from "../utils/constant";
import { capitalizeFirstLetter } from "../utils/formatters";

const Policy = () => {
  const { slug } = useParams();
  const [content, setContent] = useState("");

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchPolicy = async () => {
      setLoading(true);

      try {
        const response = await Client.get(`/public/content/${slug}`);
        if (response.status === 204) {
          setContent(chessPolicies[slug]);

          return;
        }

        if (response.data.success) {
          setContent(response.data.data);
        }
      } catch (error) {
        console.error("Error fetching content:", error);
        setContent(chessPolicies[slug]);
      } finally {
        setLoading(false);
      }
    };
    fetchPolicy();
  }, [slug]);
  return (
    <Box sx={{ minHeight: "100vh", maxWidth: "100vw", overflow: "hidden" }}>
      <Box sx={{ position: "relative", zIndex: 0 }}>
        <Box
          sx={{
            bgcolor: "white",
            minHeight: { xs: "50vh", sm: "80vh", md: "100vh", lg: "100vh" },
            display: "flex",
            flexDirection: "column",
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1,
          }}
        >
          <Box
            sx={{
              position: "relative",
              textAlign: "center",
              pb: 4,
            }}
          >
            <Box
              sx={{
                position: "relative",
                zIndex: 5,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                direction: "column",
                py: 8,
              }}
            >
              <Box
                component="img"
                src={HeroBackgroundImage}
                alt="Chess pieces"
                sx={{
                  position: "absolute",
                  width: "100%",
                  height: "auto",
                  opacity: 0.4,
                  userSelect: "none",
                  top: {
                    xs: 180,
                    sm: 100,
                    md: 160,
                    lg: 232,
                    xl: 200,
                  },
                  left: 0,
                  right: 0,
                  transform: "rotate(0.55deg)",
                  margin: "0 auto",
                  zIndex: 4,
                }}
              />
              <Box
                component="svg"
                xmlns="http://www.w3.org/2000/svg"
                preserveAspectRatio="xMidYMid meet"
                viewBox="0 0 1920 1114"
                sx={{
                  opacity: 0.9,
                  userSelect: "none",
                  position: "absolute",
                  top: 0,
                  margin: "0 auto",
                  width: {
                    xs: "150vw",
                    sm: "100%",
                    md: "100%",
                    lg: "100%",
                    xl: "100%",
                  },
                  height: "auto",
                  zIndex: -1,
                }}
              >
                <path
                  d="M754.5 1096.5L-1 833.5V0H1919.5V808.5L1193 1080C1121.4 1130.8 870.833 1112.17 754.5 1096.5Z"
                  fill="url(#paint0_linear_220_1129)"
                  fillOpacity="0.23"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_220_1129"
                    x1="1919.5"
                    y1="586.082"
                    x2="-0.999993"
                    y2="566.096"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#9F9F9F" />
                    <stop offset="0.333333" stopColor="white" />
                    <stop offset="0.666667" stopColor="white" />
                    <stop offset="1" stopColor="#9F9F9F" />
                  </linearGradient>
                </defs>
              </Box>
            </Box>
          </Box>
        </Box>

        {!loading ? (
          <Box
            sx={{
              width: "100%",
              zIndex: "1000",
              color: "#000",
              paddingX: "5vw",
              paddingY: "10vh",
              position: "relative",
              minHeight: "100vh",
            }}
          >
            <Typography
              variant="h1"
              sx={{ textAlign: "center", fontFamily: "poppins" }}
            >
              {capitalizeFirstLetter(slug.replace(/-/g, " "))}
            </Typography>

            <Box>
              <div dangerouslySetInnerHTML={{ __html: content }} />
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              width: "100%",
              zIndex: "1000",
              color: "#000",
              paddingX: "5vw",
              paddingY: "10vh",
              position: "relative",
            }}
          >
            {" "}
            <Typography
              variant="h1"
              sx={{ textAlign: "center", fontFamily: "poppins" }}
            >
              <Skeleton
                variant="text"
                width={200}
                height={32}
                sx={{ textAlign: "center" }}
              />
            </Typography>
            <Typography
              variant="h6"
              sx={{ textAlign: "start", fontFamily: "poppins", mt: "6vh" }}
            >
              {Array(20)
                .fill(0)
                .map((_, index) => (
                  <Skeleton
                    key={index}
                    variant="text"
                    width="100%"
                    height={24}
                  />
                ))}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default Policy;
