import React, { useState } from "react";
import { Button } from "@mui/material";
import ContactDetailsModel from "./ContactDetailsModal";

const ContactDetailsButton = ({
  variant = "contained",
  color = "primary",
  size = "medium",
  sx = {},
  fullWidth = false,
  data,
}) => {
  const [open, setOpen] = useState(false);

  const handleOpen = (event) => {
    event.stopPropagation();
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        variant={variant}
        color={color}
        size={size}
        onClick={handleOpen}
        sx={{
          borderRadius: "4px",
          textTransform: "none",
          fontWeight: "bold",
          ...sx,
        }}
        fullWidth={fullWidth}
      >
        Contact Details
      </Button>

      <ContactDetailsModel open={open} onClose={handleClose} data={data} />
    </>
  );
};

export default ContactDetailsButton;
