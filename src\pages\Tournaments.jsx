import React, { useState } from "react";
import { <PERSON>, Button, Container } from "@mui/material";

import { Client } from "../api/client";
import UseToast from "../lib/hooks/UseToast";
import { AxiosError } from "axios";
import DynamicTable from "../components/common/DynamicTable";
import { Link } from "react-router-dom";
import { ArrowBack } from "@mui/icons-material";
import { formatDateToDMY } from "../utils/formatters";

import TournamentSearchForm from "../components/common/TournamentSearchForm";

const TournamentsPage = () => {
  const [tournaments, setTournaments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(0);
  const [total, setTotal] = useState(0);

  // Load countries on component mount

  // States for form inputs
  const [search, setSearch] = useState({
    tournamentType: "",
    tournamentCategory: "",
    age: "",
    title: "",
    country: "",
    state: "",
    district: "",
    city: "",
    month: "",
    year: "",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  const handlePageChange = (value) => {
    setPage(value);
    fetchTournaments(value);
  };

  const handleSearch = (newPage) => {
    fetchTournaments(newPage);
  };
  const handleReset = () => {
    setSearch({
      tournamentType: "",
      tournamentCategory: "",
      age: "",
      title: "",
      country: "",
      state: "",
      district: "",
      city: "",
      month: "",
      year: "",
    });
    setPage(1);
  };
  const fetchTournaments = async (pageNumber) => {
    setLoading(true);
    try {
      const params = {
        page: pageNumber,
        limit,
      };

      // Only add non-empty search parameters
      Object.keys(search).forEach((key) => {
        if (search[key] !== "") {
          params[key] = search[key];
        }
      });

      const response = await Client.get("/tournament", { params });
      if (!response.data.success) {
        toast.info("No tournaments found");
        setTournaments([]);
        setTotal(0);
        setTotalPages(0);
        setPage(1);
        return;
      }
      if (response.status === 204) {
        toast.info("No tournaments found");
        setTournaments([]);
        setTotal(0);
        setTotalPages(0);
        setPage(1);
        return;
      }
      setTournaments(response.data.data.tournaments);
      setTotal(response.data.data.total);
      const totalPage = Math.ceil(response.data.data.total / limit);
      setTotalPages(totalPage);
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Error fetching tournaments:", error);
      toast.info("An error occurred. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 2,
        pb: 8,
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box>
        <Button sx={{ mb: 1, fontSize: 16 }} component={Link} to={-1}>
          <ArrowBack sx={{ fontSize: 16 }} /> Back
        </Button>
      </Box>
      {/* Search Form */}
      <TournamentSearchForm
        search={search}
        setSearch={setSearch}
        handleSearch={handleSearch}
        handleReset={handleReset}
      />
      {/* Tournaments Table */}
      <DynamicTable
        columns={[
          {
            id: "index",
            label: "S No",
            width: "80px",
            format: (_, item, index) => (page - 1) * limit + index + 1,
          },
          {
            id: "title",
            label: "Tournament Title",
            format: (value) => (
              <Box
                sx={{
                  textTransform: "capitalize",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  maxWidth: { xs: "300px", sm: "400px", md: "600px" },
                }}
              >
                {value.toLowerCase().replace(/-/g, " ")}
              </Box>
            ),
          },

          // {
          //   id: "startDate",
          //   label: "Start Date",
          //   format: (value) => formatDateToDMY(value),
          // },
          // {
          //   id: "endDate",
          //   label: "End Date",
          //   format: (value) => formatDateToDMY(value),
          // },
          // {
          //   id: "registrationEndDate",
          //   label: "Registration End",
          //   format: (value) => formatDateToDMY(value),
          // },
          // {
          //   id: "city",
          //   label: "Location",
          // },
        ]}
        data={tournaments}
        showBrouchureButton={true}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath="/tournaments/"
        idField="title"
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />
    </Container>
  );
};

export default TournamentsPage;
