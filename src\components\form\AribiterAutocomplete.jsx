import React, { useState, useEffect, useCallback } from "react";
import { Typography, Autocomplete, TextField, Fade, CircularProgress } from "@mui/material";
import { Controller } from "react-hook-form";
import { Client } from "../../api/client";

const ArbiterAutocomplete= ({
  name,
  control,
  placeholder = "Search for an arbiter",
  title = "Arbiter",
  sx = {},
  required = false,
  disabled = false,
  rules = {},
  fetchUrl = "/arbiter/profile/arbiter",
  defaultValue = null,
}) => {
  const [arbiters, setArbiters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [open, setOpen] = useState(false);

  // Memoize the fetch function to prevent recreation on each render
  const fetchArbiters = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.trim() === "") {
      setArbiters([]);
      return;
    }
    
    setLoading(true);
    try {
      const response = await Client.get(`${fetchUrl}?arbiterId=${encodeURIComponent(searchTerm)}`);
      setArbiters(response.data.data || []);
    } catch (error) {
      console.error("Error fetching arbiters:", error);
      setArbiters([]);
    } finally {
      setLoading(false);
    }
  }, [fetchUrl]);

  // Use debouncing to limit API calls
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (inputValue.trim()) {
        fetchArbiters(inputValue);
      } else {
        setArbiters([]);
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [inputValue, fetchArbiters]);

  return (
    <>
      <Typography
        variant="h6"
        sx={{
          textAlign: "start",
          p: "0px !important",
          m: "0px !important",
        }}
      >
        {title}
        {required && <span style={{ color: "red" }}>*</span>}
      </Typography>
      <Controller
        name={name}
        control={control}
        rules={rules}
        defaultValue={defaultValue}
        render={({
          field: { onChange, value, ref, ...field },
          fieldState: { error },
        }) => (
          <Autocomplete
            options={arbiters}
            getOptionLabel={(option) => option?.name || ""}
            value={value || null}
            sx={{
              "& .MuiInputBase-root": { p: "0px 5px !important" },
              color: "black",
              ...sx,
            }}
            onChange={(_, newValue) => {
              // Pass an empty object with null properties instead of null
              onChange(newValue || { name: null, id: null });
            }}
            onInputChange={(_, newInputValue) => {
              setInputValue(newInputValue);
            }}
            open={open && arbiters.length > 0}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            isOptionEqualToValue={(option, value) => 
              option?.id === value?.id
            }
            loading={loading}
            disabled={disabled}
            renderInput={(params) => (
              <TextField
                {...params}
                {...field}
                inputRef={ref}
                placeholder={placeholder}
                variant="outlined"
                margin="normal"
                error={!!error}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {loading ? <CircularProgress color="inherit" size={20} /> : null}
                      {params.InputProps.endAdornment}
                    </>
                  ),
                }}
                helperText={
                  error ? (
                    <Fade in={!!error}>
                      <Typography
                        component="span"
                        variant="caption"
                        color="error"
                      >
                        {error?.message}
                      </Typography>
                    </Fade>
                  ) : null
                }
              />
            )}
            renderOption={(props, option) => (
              <li {...props} key={option.id}>
                {option.name}
              </li>
            )}
          />
        )}
      />
    </>
  );
};

export default ArbiterAutocomplete;