import React, { useState } from "react";
import {
  Box,
  <PERSON>ton,
  <PERSON>alog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  IconButton,
  CircularProgress,
  Divider,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

/**
 * Modal for sending friend requests to players
 */
const FriendRequestModal = ({ open, onClose, playerData }) => {
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const toast = UseToast();

  // Reset form when modal opens
  React.useEffect(() => {
    if (open) {
      setMessage("");
    }
  }, [open]);

  // Handle sending friend request
  const handleSendRequest = async () => {
    if (!playerData?.id) {
      toast.error("Player information is missing");
      return;
    }

    setLoading(true);
    try {
      const response = await Client.post("/user/friend-request", {
        friendId: playerData?.id,
        message: message.trim() || `I'd like to add you as a friend!`,
      });

      if (response.data.success) {
        toast.success("Friend request sent successfully");
        onClose();
      } else {
        toast.error(response.data.message || "Failed to send friend request");
      }
    } catch (error) {
      console.error("Error sending friend request:", error);
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("An error occurred while sending friend request");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1,
        }}
      >
        <Typography variant="h6" component="div" fontWeight="500">
          Send Friend Request
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          disabled={loading}
          aria-label="close"
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <Divider />

      <DialogContent sx={{ pt: 2 }}>
        <Box >
          <Typography variant="subtitle1" fontWeight="500" gutterBottom>
            Send a friend request to {playerData?.name || "this player"}
          </Typography>
          <Typography variant="body2" color="black" paragraph sx={{ fontSize: "14px" ,textAlign:"start",mb:0}}>
            Add a personal message to your friend request (optional)
          </Typography>
        </Box>

        <TextField
          autoFocus
          margin="dense"
          id="message"
          
          type="text"
          fullWidth
          multiline
          rows={3}
          variant="outlined"
          sx={{mt:0}}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="I'd like to add you as a friend!"
          disabled={loading}
        />
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          onClick={onClose}
          color="inherit"
          disabled={loading}
          sx={{ borderRadius: 1, fontSize: 16 }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSendRequest}
          variant="contained"
          color="primary"
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} />}
          sx={{ borderRadius: 1, fontSize: 16 }}
        >
          {loading ? "Sending..." : "Send Request"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FriendRequestModal;
