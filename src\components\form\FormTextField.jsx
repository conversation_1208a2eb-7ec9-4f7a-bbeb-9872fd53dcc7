import React from "react";
import { Typography, TextField as Mui<PERSON><PERSON><PERSON><PERSON><PERSON>, Fade } from "@mui/material";
import { Controller } from "react-hook-form";

// Custom TextField component that works with React Hook Form
const FormTextField = React.memo(
  ({
    name,
    control,
    placeholder,
    title,
    required = false,
    rules,
    type = "text",
    inputProps,
    maxLength,
    specialCharAllowed = false,
    ...rest
  }) => {
    return (
      <>
        <Typography
          variant="h6"
          sx={{
            textAlign: "start",
            p: "0px !important",
            m: "0px !important",
            fontSize: 20,
          }}
        >
          {title}
          {required && <span style={{ color: "red" }}>*</span>}
        </Typography>
        <Controller
          name={name}
          control={control}
          rules={rules}
          render={({ field, fieldState: { error } }) => {
            const handleChange = (e) => {
              const rawValue = e.target.value;
              // Allow alphanumeric, spaces, and common email special characters
              const sanitized = specialCharAllowed
                ? rawValue
                : rawValue.replace(/[^a-zA-Z0-9\s-]/g, "");

              field.onChange(sanitized);
            };

            return (
              <MuiTextField
                {...field}
                fullWidth
                variant="outlined"
                margin="normal"
                placeholder={placeholder}
                type={type}
                onChange={handleChange}
                inputProps={{ ...inputProps, maxLength }}
                sx={{
                  minHeight: 70,
                  "& .MuiInputBase-input": { padding: "8px 14px" },
                  ...rest?.sx,
                }}
                error={!!error}
                helperText={
                  <Fade in={!!error}>
                    <Typography
                      component="span"
                      variant="caption"
                      color="error"
                    >
                      {error?.message}
                    </Typography>
                  </Fade>
                }
                {...rest}
              />
            );
          }}
        />
      </>
    );
  }
);

export default FormTextField;
