import React, { useEffect, useState } from 'react';
import axios from 'axios';
import {
  Box,
  Typography,
  Grid,
  Paper,
  CircularProgress,
  Card,
  CardContent,
} from '@mui/material';
import { Client } from '../../api/client';
import PaymentIcon from "@mui/icons-material/Payment";
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';

const AdminPaymentDetails = () => {
  const [data, setData] = useState({
    totalTournaments: 0,
    totalEarnings: 0,
    totalEarned: 0,
  });
  const [loading, setLoading] = useState(true);

  const fetchData = async () => {
    try {
      const response = await Client.get('/admin/details/payment');
      const { tournamentCount, totalAmount, totalEarnings } = response.data.data;
      setData({ totalTournaments: tournamentCount, totalEarnings: totalAmount, totalEarned: totalEarnings });
    } catch (error) {
      console.error('Error fetching payment details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" mt={5}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper sx={{ mb: 3, p: 3, bgcolor: "#f5f9ff", borderRadius: 2, boxShadow: 2 }}>
      <Typography
        variant="h6"
        gutterBottom
        sx={{ fontWeight: "bold", color: "#3f51b5" }}
      >
        Payment Summary
      </Typography>
      <Grid container spacing={3} sx={{ mt: 1 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: "#e3f2fd", height: "100%" }}>
            <CardContent>
              <Typography variant="subtitle2" color="primary">
                Total Tournament
              </Typography>
              <Typography
                variant="h4"
                sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
              >

                {data.totalTournaments || 0}
              </Typography>
              <Typography variant="body2" color="primary" sx={{ mt: 1 }}>
                <EmojiEventsIcon
                  fontSize="medium"
                  sx={{ verticalAlign: "middle", mr: 0.5 }}
                />
                Tournaments
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
            <CardContent>
              <Typography variant="subtitle2" color="secondary">
                Total Amount
              </Typography>
              <Typography
                variant="h4"
                sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
              >

                ₹{data.totalEarnings?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                <PaymentIcon
                  fontSize="small"
                  sx={{ verticalAlign: "middle", mr: 0.5 }}
                />
                Collected
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: "#f3e5f5", height: "100%" }}>
            <CardContent>
              <Typography variant="subtitle2" color="primary">
                Total Earnings
              </Typography>
              <Typography 
                variant="h4"
                sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}>
                ₹{data.totalEarned?.toLocaleString() || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: "#e8f5e9", height: "100%" }}>
            <CardContent>
              <Typography variant="subtitle2" color="secondary">
                Total Refund Amount
              </Typography>
              <Typography
                variant="h4"
                sx={{ mt: 1, fontWeight: "bold", textAlign: "center" }}
              >

                ₹{data.refundAmount?.toLocaleString() || 0}
              </Typography>
              <Typography variant="body2" color="secondary" sx={{ mt: 1 }}>
                <PaymentIcon
                  fontSize="small"
                  sx={{ verticalAlign: "middle", mr: 0.5 }}
                />
                Refunded
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
      </Grid>
    </Paper>
  );
};

export default AdminPaymentDetails;