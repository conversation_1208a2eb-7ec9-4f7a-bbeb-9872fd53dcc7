import { useState } from "react";
import { Box, Avatar, IconButton, Typography } from "@mui/material";
import { Edit } from "@mui/icons-material";
import compressImage from "../../utils/imageCompressor";

const ProfileImageUpload = ({ control, setValue, watch }) => {
  const profileImagePreview = watch("profileImagePreview");
  const [uploadError, setUploadError] = useState(null);
  const [isCompressing, setIsCompressing] = useState(false);

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    setUploadError(null);

    if (!file) return;

    // Validate file type
    if (
      !["image/jpeg", "image/png", "image/jpg", "image/webp"].includes(
        file.type
      )
    ) {
      setUploadError("Please upload a JPEG, PNG, or WebP image");
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setUploadError("File must be less than 5MB");
      return;
    }

    setIsCompressing(true);

    try {
      // Compress the image to approximately 500KB
      const compressedFile = await compressImage(file, 500);

      // Set the compressed file for submission
      setValue("profileImage", compressedFile);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setValue("profileImagePreview", e.target.result);
      };
      reader.readAsDataURL(compressedFile);

    } catch (error) {
      console.error("Error compressing image:", error);
      setUploadError("Error processing image");
    } finally {
      setIsCompressing(false);
    }
  };

  return (
    <Box
      sx={{ position: "relative", width: 120, height: 120, margin: "0 auto" }}
    >
      <Avatar
        sx={{
          width: "100%",
          height: "100%",
          border: "2px solid #ccc",
        }}
        src={profileImagePreview || control._defaultValues.profileUrl}
        alt="Profile"
      />

      <IconButton
        component="label"
        htmlFor="profile-image-upload"
        sx={{
          position: "absolute",
          right: -10,
          bottom: -10,
          backgroundColor: "primary.main",
          "&:hover": { backgroundColor: "primary.dark" },
          borderRadius: "50%",
          padding: "8px",
        }}
        disabled={isCompressing}
      >
        <Edit sx={{ fontSize: 16, color: "white" }} />
        <input
          id="profile-image-upload"
          type="file"
          hidden
          accept="image/jpeg, image/png, image/jpg, image/webp"
          onChange={handleFileChange}
        />
      </IconButton>

      {isCompressing && (
        <Typography
          variant="caption"
          sx={{ display: "block", textAlign: "center", mt: 1 }}
        >
          Loading...
        </Typography>
      )}

      {uploadError && (
        <Typography
          color="error"
          variant="caption"
          sx={{ display: "block", textAlign: "center", mt: 1 }}
        >
          {uploadError}
        </Typography>
      )}
    </Box>
  );
};

export default ProfileImageUpload;
