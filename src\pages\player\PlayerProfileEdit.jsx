import React from "react";
import PlayerProfileForm from "../../components/form/PlayerProfileForm";
import { Box, Container, Typography } from "@mui/material";
import BackButton from "../../components/common/BackButton";

const PlayerProfileEdit = () => {
  const [edit] = React.useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editParam = urlParams.get("edit");
    return editParam === "1";
  });

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton to={"/dashboard"} />
      <Box maxWidth="lg" sx={{ margin: "auto", my: 2 }}>
        <Typography variant="h4" sx={{ mb: 6 }}>
          My Profile
        </Typography>

        <PlayerProfileForm edit={edit} />
      </Box>
    </Container>
  );
};

export default PlayerProfileEdit;
