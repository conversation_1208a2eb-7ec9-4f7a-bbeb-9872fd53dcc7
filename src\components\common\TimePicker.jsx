import React from "react";
import {
  Grid,
  Select,
  MenuItem,
  FormControl,
  FormHelperText,
} from "@mui/material";

const TimePicker = ({ value, onChange, error, helperText }) => {
  // Generate hours (1-12)
  const hours = Array.from({ length: 12 }, (_, i) => i + 1);

  // Generate minutes (00-55 in 5-minute increments)
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5).map((min) =>
    min.toString().padStart(2, "0")
  );

  // Parse the time string into hour, minute, and period
  const parseTime = (timeString = "9:00 AM") => {
    if (!timeString) return { hour: 9, minute: "00", period: "AM" };

    const match = timeString.match(/^(\d{1,2}):(\d{2})\s?(AM|PM)?$/i);
    if (!match) return { hour: 9, minute: "00", period: "AM" };

    let [, h, m, p = "AM"] = match;
    let hour = parseInt(h, 10) || 12;
    let minute = m || "00";
    let period = (p || "AM").toUpperCase();

    return { hour, minute, period };
  };

  const currentTime = parseTime(value);

  const handleTimeChange = (field, newValue) => {
    const updatedTime = { ...currentTime, [field]: newValue };

    // Format hour with leading zeros
    const finalHour = parseInt(updatedTime.hour, 10) || 12; // Default to 12 if invalid
    const paddedHour = finalHour.toString().padStart(2, "0");
    
    // Ensure valid minute value with leading zeros
    const finalMinute = updatedTime.minute || "00";
    
    // Format time string according to the expected format: "hh:mm AM/PM"
    const formattedTime = `${paddedHour}:${finalMinute} ${updatedTime.period}`;
    
    onChange(formattedTime);
  };

  return (
    <Grid container spacing={2} alignItems="center" sx={{ mt: 0 }}>
      <Grid item xs={4} sx={{ mt: 0, pt: "0px !important" }}>
        <FormControl fullWidth sx={{ mt: "2px !important" }}>
          <Select
            value={currentTime.hour}
            onChange={(e) => handleTimeChange("hour", e.target.value)}
            displayEmpty
            renderValue={(selected) => (selected ? selected : "Hour")}
          >
            {hours.map((hour) => (
              <MenuItem key={hour} value={hour}>
                {hour}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={4} sx={{ mt: 0, pt: "0px !important" }}>
        <FormControl fullWidth sx={{ mt: "2px !important" }}>
          <Select
            value={currentTime.minute}
            onChange={(e) => handleTimeChange("minute", e.target.value)}
            displayEmpty
            renderValue={(selected) => (selected ? selected : "Min")}
          >
            {minutes.map((minute) => (
              <MenuItem key={minute} value={minute}>
                {minute}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={4} sx={{ mt: 0, pt: "0px !important" }}>
        <FormControl fullWidth sx={{ mt: "2px !important" }}>
          <Select
            value={currentTime.period}
            onChange={(e) => handleTimeChange("period", e.target.value)}
          >
            <MenuItem value="AM">AM</MenuItem>
            <MenuItem value="PM">PM</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      {error && (
        <Grid item xs={12}>
          <FormHelperText error>{helperText}</FormHelperText>
        </Grid>
      )}
    </Grid>
  );
};

export default TimePicker;