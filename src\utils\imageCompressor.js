import imageCompression from "browser-image-compression";
const compressImage = async (file, maxSizeKB = 500) => {
  // Return original file if it's already smaller than target size
  if (file.size <= maxSizeKB * 1024) {
    return file;
  }

  try {
    // Compress image using browser-image-compression library
    const options = {
      maxSizeMB: maxSizeKB / 1024, // Convert KB to MB
      maxWidthOrHeight: 1920, // Limit width/height if needed
      useWebWorker: true, // Use web worker for better performance
      preserveExif: true, // Keep image metadata
      fileType: "image/jpeg",
    };

    const compressedBlob = await imageCompression(file, options);

    const compressedFile = new File(
      [compressedBlob],
      file.name, // or generate a new name if needed
      {
        type: compressedBlob.type,
        lastModified: Date.now(),
      }
    );

    return compressedFile;
  } catch (error) {
    console.error("Error compressing image:", error);
    // Return original file if compression fails
    return file;
  }
};

export default compressImage;
