import { useEffect, useState } from "react";

function useUserGeoInfo() {
  const [geoInfo, setGeoInfo] = useState(
    JSON.parse(localStorage.getItem("Geolocation"))
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchGeoInfo = async () => {
      // Check localStorage first

      if (geoInfo) {
        setGeoInfo(geoInfo);
        setLoading(false);
        return;
      }

      try {
        const geoResponse = await fetch(
          `https://ipinfo.io/json?token=f6d85870a3e58b`
        );
        const geo = await geoResponse.json();
        // return
        const locationData = {
          ip: geo.ip,
          city: geo.city,
          state: geo.region,
          country: geo.country,
        };

        // Store to localStorage
        localStorage.setItem("Geolocation", JSON.stringify(locationData));
        setGeoInfo(locationData);
      } catch (err) {
        console.error("Error fetching geolocation:", err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchGeoInfo();
  }, []);

  return { geoInfo, loading, error };
}

export default useUserGeoInfo;
