import React from "react";
import {
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputAdornment,
  Box,
} from "@mui/material";
import { Controller } from "react-hook-form";

const FormInputSelectField = ({
  name,
  control,
  title,
  placeholder,
  required = false,
  rules = {},
  options,
  sx = {},
  max,
}) => {
  return (
    <Box sx={{ mb: 2, ...sx }} key={name}>
      <Typography variant="h6" sx={{ textAlign: "start" }}>
        {title} {required && <span style={{ color: "red" }}>*</span>}
      </Typography>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field, fieldState: { error } }) => {
          // Parse the current string value without useMemo
          const parseFieldValue = () => {
            if (!field.value) return ["", options[0]?.value || ""];

            // Split the string value (e.g., "20 min") into parts
            const parts = field.value.split(" ");
            if (parts.length >= 2) {
              return [parts[0], parts[1]];
            }

            // If the value is just a number without unit, return it with default unit
            if (!isNaN(parseInt(field.value))) {
              return [field.value, options[0]?.value || ""];
            }

            return ["", options[0]?.value || ""];
          };

          const [numericValue, unitValue] = parseFieldValue();

          return (
            <FormControl
              fullWidth
              error={!!error}
              sx={{
                "& .MuiInputBase-input": { padding: "8px 14px" },
                mt: "0px !important",
              }}
            >
              <TextField
                placeholder={placeholder}
                type="number"
                error={!!error}
                value={numericValue}
                onChange={(e) => {
                  let inputVal = e.target.value;

                  // Enforce max value manually
                  if (max && inputVal) {
                    const numeric = parseFloat(inputVal);
                    if (numeric > max) inputVal = max.toString();
                  }

                  const newValue = inputVal ? `${inputVal} ${unitValue}` : "";
                  field.onChange(newValue);
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <Select
                        value={unitValue}
                        onChange={(e) => {
                          e.stopPropagation();
                          // Combine current numeric value with new unit
                          const newValue = numericValue
                            ? `${numericValue} ${e.target.value}`
                            : "";
                          field.onChange(newValue);
                        }}
                        onClick={(e) => e.stopPropagation()}
                        variant="standard"
                        sx={{
                          minWidth: 65,
                          "& .MuiSelect-select": {
                            py: 0.5,
                            pl: 1,
                            pr: 2,
                          },
                          "& .MuiInput-root": {
                            "&:before, &:after": {
                              display: "none",
                            },
                          },
                        }}
                        disableUnderline
                      >
                        {options.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </InputAdornment>
                  ),

                  sx: {
                    "& .MuiInputAdornment-root": {
                      ml: 0,
                      height: "100%",
                      maxHeight: "100%",
                    },
                  },
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    pr: 0,
                    "& .MuiInputAdornment-root": {
                      bgcolor: "action.hover",
                      height: "100%",
                      maxHeight: "100%",
                      borderTopRightRadius: 4,
                      borderBottomRightRadius: 4,
                      marginLeft: 0,
                      borderLeft: "1px solid",
                      borderLeftColor: "divider",
                    },
                  },
                }}
              />
              {error && (
                <Typography variant="caption" color="error">
                  {error.message}
                </Typography>
              )}
            </FormControl>
          );
        }}
      />
    </Box>
  );
};

export default FormInputSelectField;
