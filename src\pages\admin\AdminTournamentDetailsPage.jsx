import React, { useEffect, useState } from "react";
import { <PERSON>, Button, Container, Alert, Typography } from "@mui/material";
import { Link, useNavigate, useParams } from "react-router-dom";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import PaymentModal from "../../components/payment/PaymentModal";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import TournamentDetailsView from "../../components/common/TournamentDetailsView";
import TournamentDetailsSkeleton from "../../components/skeletons/TournamentDetailsSkeleton";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import {
  isTournamentConducted,
  isTournamentOngoing,
  isWithinRegistrationPeriod,
} from "../../utils/utils";
import BackButton from "../../components/common/BackButton";

const AdminTournamentDetailsPage = () => {
  const { title } = useParams();
  const newTitle = encodeURIComponent(title);

  const [tournament, setTournament] = useState({});
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState([])

  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [isRegistered, setIsRegistered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [tournamentOngoing, setTournamentOngoing] = useState(false);
  const [withinRegistrationPeriod, setWithinRegistrationPeriod] =
    useState(false);
  const [tournamentConducted, setTournamentConducted] = useState(false);

  const { user, setOpenModel, isLoggedIn } = useGlobalContext();
  const toast = UseToast();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDetails = async () => {
      if (!title) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${newTitle}`);
        if (response.data.success) {
          setTournament(response.data.data);

          const startDate = response.data?.data?.startDate;
          const endDate = response.data?.data?.endDate;
          const reportingTime = response.data?.data?.reportingTime;

          const ongoing = isTournamentOngoing(
            startDate,
            endDate,
            reportingTime
          );
          setTournamentOngoing(ongoing);

          const start = response.data?.data?.registrationStartDate;
          const end = response.data?.data?.registrationEndDate;
          const endTime = response.data?.data?.registrationEndTime;

          const isOpen = isWithinRegistrationPeriod(start, end, endTime);
          setWithinRegistrationPeriod(isOpen);
          const conducted = isTournamentConducted(endDate);
          setTournamentConducted(conducted);
        } else {
          toast.info(
            response.data.message || "Failed to load tournament details"
          );
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error(
          "Error fetching tournament details. Please try again later."
        );
      } finally {
        setLoading(false);
      }
    };

    fetchDetails();
  }, [newTitle, user]);
  const getFileExtension = (mimeType) => {
    const extensions = {
      "image/png": ".png",
      "image/jpeg": ".jpg",
      "image/jpg": ".jpg",
      "application/pdf": ".pdf",
      "application/msword": ".doc",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        ".docx",
      "text/plain": ".txt",
      "application/vnd.ms-excel": ".xls",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        ".xlsx",
      "application/vnd.ms-powerpoint": ".ppt",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        ".pptx",
    };
    return extensions[mimeType] || "";
  };

  const handleBrochureDownload = async ({ id, title }) => {
    try {
      // Request a short-lived pre-signed URL from your backend
      const response = await Client.get(`/tournament/${id}/brochure`);
      const presignedUrl = response.data.data;

      if (!presignedUrl) {
        throw new Error("No download URL received from server");
      }

      // Use the pre-signed URL (typically valid for 5-15 minutes)
      const fileResponse = await fetch(presignedUrl);

      if (!fileResponse.ok) {
        throw new Error(
          `Failed to download file: ${fileResponse.status} ${fileResponse.statusText}`
        );
      }

      const blob = await fileResponse.blob();

      if (blob.size === 0) {
        throw new Error("Downloaded file is empty");
      }

      // Sanitize filename to remove invalid characters
      const sanitizedTitle =
        title.replace(/[<>:"/\\|?*]/g, "").trim() || "download";

      // Get extension from Content-Type header first, then fallback to blob type
      const contentType = fileResponse.headers.get("content-type");
      const extension =
        getFileExtension(contentType) || getFileExtension(blob.type) || "";

      // Download the file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", sanitizedTitle + extension);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading document:", error);
      // You might want to show a user-friendly error message here
      throw error;
    }
  };

  const handleRemindMe = async () => {
    if (!isLoggedIn) {
      // Redirect to login or show login modal
      setOpenModel((prev) => ({ ...prev, login: true }));
      return;
    }

    try {
      setIsLoading(true);
      const response = await Client.post(
        `/player/tournaments/${tournament.title}/reminder`
      );
      if (!response.data.success) {
        toast.error(response.data.message);
        return;
      }

      toast.success(response.data.data.message);
    } catch (error) {
      toast.error(error.response.data.error || "Unable to set reminder");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    const checkRegistrationStatus = async () => {
      if (
        !user ||
        !title ||
        !tournament ||
        !tournament.id ||
        !user?.role === "player"
      )
        return;

      try {
        const response = await Client.get(
          `/tournament/${newTitle}/register/status`,
          { params: { tournamentId: tournament.id } }
        );

        if (response.data.success) {
          const { isRegistered } = response.data.data;

          setIsRegistered(isRegistered);
        }
      } catch (error) {
        console.error("Error checking registration status:", error);
      }
    };

    if (tournament.id) {
      checkRegistrationStatus();
    }
  }, [user, title, tournament.id, tournament]);

  // Check eligibility before registration

  // Handle tournament registration button click
  const handleRegister = async () => {
    if (!user) {
      toast.error("Please login to register for the tournament");
      setOpenModel((prev) => ({ ...prev, login: true }));
      return false;
    }

    setPaymentModalOpen(true);
  };

    const fetchPlayers = async (type) => {
      setLoading(true);
      try {
        const response = await Client.get(`club/tournament/${newTitle}/players`, {
        });
        if (!response.data.success) {
          toast.info(response.data.error.massage);
          return;
        }
        if (response.data.status === 204) {
          toast.info("No Player found");
          setPlayers([]);
          setPage(0);
          setTotal(0);
          setTotalPages(0);
        }
        const data = response.data?.data;
        setPlayers(response.data.data.players);
        console.log("type",type)
        navigate(`/dashboard/${type}/compose`, {
          state: {
            selectedUsers:response.data.data.players,
            selectedType: "player",
            mode: type,
          },
        });
      } catch (error) {
        console.error("Error searching players:", error);
        toast.error("An error occurred while searching for players");
      } finally {
        setLoading(false);
      }
    };

  return (
    <>
      {loading ? (
        <TournamentDetailsSkeleton />
      ) : (
        <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "100vh" }}>
          {/* Payment Success Alert */}
          <BackButton />

          <div style={{ minHeight: "100vh" }}>
            {tournament && <TournamentDetailsView tournaments={tournament} />}
          </div>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexWrap: "wrap",
              justifyContent: "center",
              gap: 2,
              p: 2,
              borderBottom: "1px solid #f0f0f0",
            }}
          >

            <Button
              variant="contained"
              color="success"
              onClick={()=>fetchPlayers('email')}
              sx={{
                borderRadius: 1,
                textTransform: "none",
                fontWeight: 500,
                fontSize: 16,
                px: 2,
                bgcolor: "#166DA3", // Default background
                "&:hover": {
                  bgcolor: "#1A7FBF", // Lighter blue on hover
                  boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(-2px)",
                  transition: "all 0.3s ease",
                },
                "&:active": {
                  bgcolor: "#125B87", // Slightly darker blue for active click
                  boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(0)", // Reset push on click
                },
              }}
            >
              Mail
            </Button>

            
            <Button
              variant="contained"
              color="success"
              onClick={()=>fetchPlayers('sms')}
              sx={{
                borderRadius: 1,
                textTransform: "none",
                fontWeight: 500,
                fontSize: 16,
                px: 2,
                bgcolor: "#166DA3", // Default background
                "&:hover": {
                  bgcolor: "#1A7FBF", // Lighter blue on hover
                  boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(-2px)",
                  transition: "all 0.3s ease",
                },
                "&:active": {
                  bgcolor: "#125B87", // Slightly darker blue for active click
                  boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                  transform: "translateY(0)", // Reset push on click
                },
              }}
            >
              Sms
            </Button>

            {/* {tournament.locationUrl && (
              <Button
                variant="contained"
                color="success"
                type="a"
                target="_blank"
                rel="noopener noreferrer"
                href={
                  tournament && tournament.locationUrl
                    ? tournament.locationUrl
                    : "#"
                }
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  px: 2,
                  bgcolor: "#166DA3", // Default background
                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },
                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                View Map
              </Button>
            )}
            {tournament.chatUrl && isLoggedIn && (
              <Button
                variant="contained"
                color="success"
                type="a"
                target="_blank"
                rel="noopener noreferrer"
                href={
                  tournament && tournament.chatUrl ? tournament.chatUrl : "#"
                }
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  px: 2,
                  bgcolor: "#166DA3", // Default background
                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },
                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Chat Now
              </Button>
            )}
            {tournament.brochureUrl && (
              <Button
                variant="contained"
                onClick={() =>
                  handleBrochureDownload({
                    id: tournament.id,
                    title: tournament.title,
                  })
                }
                color="success"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  px: 2,
                  bgcolor: "#166DA3", // Default background
                  color: "#fff", // Text color for good contrast
                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },
                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Download Brochure
              </Button>
            )}
            {new Date() <= new Date(tournament.registrationStartDate) && (
              <Button
                fullWidth
                onClick={handleRemindMe}
                disabled={isLoading}
                color="primary"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  color: "white",
                  px: 2,
                  maxWidth: "250px",
                  bgcolor: "#166DA3", // Default background
                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },
                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Remind me
              </Button>
            )}
            {new Date(tournament.registrationStartDate) <= new Date() && (
              <>
                <Button
                  fullWidth
                  component={Link}
                  to="registered-players"
                  color="primary"
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    color: "white",
                    px: 2,
                    maxWidth: "250px",
                    bgcolor: "#166DA3", // Default background
                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },
                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Registered Players
                </Button>
                <Button
                  fullWidth
                  component={Link}
                  to="pairing-details"
                  color="primary"
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    color: "white",
                    px: 2,
                    maxWidth: "250px",
                    bgcolor: "#166DA3", // Default background
                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },
                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Pairing details
                </Button>
              </>
            )}
            {(tournamentOngoing || tournamentConducted) && (
              <Button
                fullWidth
                component={Link}
                to="leader-board"
                color="primary"
                sx={{
                  borderRadius: 1,
                  textTransform: "none",
                  fontWeight: 500,
                  fontSize: 16,
                  color: "white",
                  px: 2,
                  maxWidth: "250px",
                  bgcolor: "#166DA3", // Default background
                  "&:hover": {
                    bgcolor: "#1A7FBF", // Lighter blue on hover
                    boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease",
                  },
                  "&:active": {
                    bgcolor: "#125B87", // Slightly darker blue for active click
                    boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                    transform: "translateY(0)", // Reset push on click
                  },
                }}
              >
                Leader Board
              </Button>
            )}
            {user && user?.role === "player" && isRegistered ? (
              <Alert
                severity="success"
                sx={{
                  width: { xs: "100%", lg: "auto" },
                }}
              >
                <Typography variant="h6" sx={{ fontSize: "16px" }}>
                  You are registered for this tournament
                </Typography>
              </Alert>
            ) : (
              user &&
              user?.role === "player" &&
              withinRegistrationPeriod && (
                <Button
                  variant="contained"
                  color="success"
                  onClick={handleRegister}
                  disabled={loading}
                  sx={{
                    borderRadius: 1,
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: 16,
                    px: 2,
                    color: "white",
                    bgcolor: "#166DA3", // Default background
                    "&:hover": {
                      bgcolor: "#1A7FBF", // Lighter blue on hover
                      boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease",
                    },
                    "&:active": {
                      bgcolor: "#125B87", // Slightly darker blue for active click
                      boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                      transform: "translateY(0)", // Reset push on click
                    },
                  }}
                >
                  Register Now
                </Button>
              )
            )}
            {user && user?.role === "club" && (
              <>
                {withinRegistrationPeriod && (
                  <Button
                    variant="contained"
                    color="success"
                    href={`/dashboard/tournaments/${newTitle}/bulk-registration`}
                    sx={{
                      borderRadius: 1,
                      textTransform: "none",
                      fontWeight: 500,
                      fontSize: 16,
                      px: 2,
                      bgcolor: "#166DA3", // Default background
                      "&:hover": {
                        bgcolor: "#1A7FBF", // Lighter blue on hover
                        boxShadow: "0 6px 12px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(-2px)",
                        transition: "all 0.3s ease",
                      },
                      "&:active": {
                        bgcolor: "#125B87", // Slightly darker blue for active click
                        boxShadow: "inset 0 3px 6px rgba(0, 0, 0, 0.2)",
                        transform: "translateY(0)", // Reset push on click
                      },
                    }}
                  >
                    Register Players
                  </Button>
                )}
              </>
            )} */}
          </Box>

          {/* Payment Modal */}
          {user && user?.role === "player" && (
            <PaymentModal
              open={paymentModalOpen}
              setLoading={setLoading}
              onClose={() => setPaymentModalOpen(false)}
              tournament={tournament}
            />
          )}
        </Container>
      )}
    </>
  );
};

export default AdminTournamentDetailsPage;