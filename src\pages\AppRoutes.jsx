import React, { lazy, Suspense } from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import useGlobalContext from "../lib/hooks/UseGlobalContext";
import MainLayout from "../components/MainLayout";

import Spinner from "../components/common/Spinner";
import { Payment } from "@mui/icons-material";
import ContactUs from "./ContactUs";
import Policy from "./Policy";

import LandingPage from "./LandingPage";

import ComingSoon from "./ComingSoon";
import NotFound from "./NotFound";
import AdminDashboard from "./admin/Dashboard";
import ContentPage from "./admin/ContentPage";
import AboutUs from "./AboutUs";
import ArbiterDetails from "./admin/arbiterDetails";
import ClubsDetails from "./admin/clubDetails";
import MyCertificatesPage from "./player/MyCertificates";

const ClubProfileEdit = lazy(() => import("./club/ClubProfileEdit"));
const TournamentView = lazy(() => import("./club/TournamentView"));
const ClubTournamentsPage = lazy(() => import("./club/ClubTournamentsPage"));

const ArbiterTournamentsPage = lazy(() =>
  import("./arbiters/ArbiterTournamentsPage")
);
const ArbiterPairingDetails = lazy(() =>
  import("./arbiters/ArbiterPairingDetails")
);
const CertificatesPage = lazy(() => import("./club/CertificatesPage"));

const AdminDetailsPage = lazy(() => import("./admin/adminDetails"));
const AdminPlayersPage = lazy(() => import("./admin/AdminPlayersPage"));
const AdminClubsPage = lazy(() => import("./admin/AdminClubsPage"));
const AdminArbitersPage = lazy(() => import("./admin/AdminArbitersPage"));
const PlayerDetails = lazy(() => import("./admin/PlayerDetails"));
const AdminAdUploadPage = lazy(() => import("./admin/AdminAdUploadPage"));
const AdminTournamentsPage = lazy(() => import("./admin/AdminTournamentPage"));
const AdminTournamentDetailsPage = lazy(() =>
  import("./admin/AdminTournamentDetailsPage")
);
const AdminPaymentPage = lazy(() => import("./admin/AdminPayment"));
const PaymentTournamentDetails = lazy(() =>
  import("../components/admin/PaymentDetails")
);
const MessagingPage = lazy(() => import("./admin/MessagingPage"));
const EmailComposer = lazy(() => import("./admin/EmailComposer"));
const SMSComposer = lazy(() => import("./admin/SMSComposer"));
const WhatsAppComposer = lazy(() => import("./admin/WhatsAppComposer"));

// Lazy load pages for better performance

const ClubsPage = lazy(() => import("./club/ClubsPage"));
const ClubsDetailsPage = lazy(() => import("./club/ClubsDetailsPage"));
const PlayersPage = lazy(() => import("./player/PlayersPage"));
const PlayerDetailsPage = lazy(() => import("./player/PlayerDetailsPage"));
const TournamentsPage = lazy(() => import("./Tournaments"));
const TournamentDetailsPage = lazy(() => import("./TournamentDetailsPage"));
const PaymentFailurePage = lazy(() => import("./PaymentFailurePage"));
const PaymentSuccessPage = lazy(() => import("./PaymentSuccessPage"));

// Dashboard pages
const TournamentHistoryPage = lazy(() => import("./player/TournamentHistory"));
const PlayerDashBoard = lazy(() => import("./player/Dashboard"));
const PlayerProfilePage = lazy(() => import("./player/ProfilePage"));
const PlayerProfileEdit = lazy(() => import("./player/PlayerProfileEdit"));
const MyDocsPage = lazy(() => import("./player/MyDocsPage"));
const ClubDashboard = lazy(() => import("./club/DashBoard"));
const CreateTournamentPage = lazy(() => import("./club/CreateTournamentPage"));
const ClubProfilePage = lazy(() => import("./club/ClubProfile"));
const ArbiterDashboard = lazy(() => import("./arbiters/DashBoard"));
const ClubBankingDetails = lazy(() => import("./club/ClubBankingDetails"));
const EditTournamentPage = lazy(() => import("./club/EditTournament"));
const ClubMembersPage = lazy(() => import("./club/ClubMembers"));
const PaymentHistoryPage = lazy(() => import("./player/PaymentHistory"));
const ClubPaymentHistoryPage = lazy(() => import("./club/PaymentHistory"));
const LeaderBoard = lazy(() => import("./tournament/LeaderBoard"));
const AribiterLeaderBoard = lazy(() => import("./arbiters/ArbiterLeaderBoard"));
const PairingDetails = lazy(() => import("./tournament/PairingDetails"));
const AttendanceMarkingPage = lazy(() =>
  import("./club/AttendanceMarkingPage")
);
const PlayerNotificationsPage = lazy(() =>
  import("./player/NotificationsPage")
);
const ClubNotificationsPage = lazy(() => import("./club/NotificationsPage"));
const TournamentEarningsPage = lazy(() => import("./club/TournamentEarnings"));
const BulkRegistrationPage = lazy(() =>
  import("./tournament/BulkRegistrationPage")
);
const SelectedPlayers = lazy(() =>
  import("../components/registration/SelectedPlayers")
);
const RegisteredPlayers = lazy(() => import("./club/RegisteredPlayers"));

const ArbitersPage = lazy(() => import("./arbiters/ArbitersPage"));
const ArbiterDetailsPage = lazy(() => import("./arbiters/ArbiterDetailsPage"));
const ArbiterProfilePage = lazy(() => import("./arbiters/ArbiterProfile"));
const ArbiterProfileEdit = lazy(() => import("./arbiters/ArbiterProfileEdit"));
const ArbiterNotificationPage = lazy(() =>
  import("./arbiters/ArbiterNotificationPage")
);

// // Placeholder components (you'll want to replace these with actual implementations)
// const BankingDetailsPage = lazy(() =>
//   import("../components/placeholders/BankingDetails")
// );
// const ClubMembersPage = lazy(() =>
//   import("../components/placeholders/ClubMembers")
// );
// const PaymentHistoryPage = lazy(() =>
//   import("../components/placeholders/PaymentHistory")
// );

// Role-based route configurations
const ROUTE_CONFIG = {
  player: [
    { path: "dashboard", element: <PlayerDashBoard /> },
    { path: "dashboard/profile", element: <PlayerProfilePage /> },
    { path: "dashboard/profile/edit", element: <PlayerProfileEdit /> },
    { path: "dashboard/payments-history", element: <PaymentHistoryPage /> },
    {
      path: "dashboard/tournament-history",
      element: <TournamentHistoryPage />,
    },
    { path: "dashboard/my-docs", element: <MyDocsPage /> },
    { path: "dashboard/notifications", element: <PlayerNotificationsPage /> },
    {
      path: "dashboard/notifications/friend-requests",
      element: <PlayerNotificationsPage />,
    },
    {
      path: "dashboard/notifications/club-invites",
      element: <PlayerNotificationsPage />,
    },
    { path: "dashboard/my-certificate", element: <MyCertificatesPage /> },
    { path: "dashboard/solve-puzzles", element: <ComingSoon /> },
    { path: "dashboard/pay-online", element: <ComingSoon /> },
    { path: "dashboard/my-chats", element: <ComingSoon /> },
    { path: "dashboard/history", element: <ComingSoon /> },
  ],
  club: [
    { path: "dashboard", element: <ClubDashboard /> },
    { path: "dashboard/createtournament", element: <CreateTournamentPage /> },
    { path: "dashboard/profile", element: <ClubProfilePage /> },
    { path: "dashboard/profile/edit", element: <ClubProfileEdit /> },
    { path: "dashboard/bankingdetails", element: <ClubBankingDetails /> },
    { path: "dashboard/tournaments", element: <ClubTournamentsPage /> },
    { path: "dashboard/tournaments/:title", element: <TournamentView /> },
    {
      path: "dashboard/tournaments/:title/attendance",
      element: <AttendanceMarkingPage />,
    },
    {
      path: "dashboard/tournaments/:title/leader-board",
      element: <LeaderBoard />,
    },
    {
      path: "dashboard/tournaments/:title/pairing-details",
      element: <PairingDetails />,
    },
    {
      path: "dashboard/tournaments/:title/certificates",
      element: <CertificatesPage />,
    },
    {
      path: "dashboard/tournaments/:title/bulk-registration",
      element: <BulkRegistrationPage />,
    },
    {
      path: "dashboard/tournaments/:title/bulk-registration/register/:id",
      element: <SelectedPlayers />,
    },
    // {
    //   path: "dashboard/tournaments/:title/bulk-registration",
    //   element: <BulkRegistrationPage />,
    //   children: [
    //     {
    //       path: "register/:id",
    //       element: <SelectedPlayers />
    //     }
    //   ]
    // },
    {
      path: "dashboard/tournaments/:title/registeredplayers",
      element: <RegisteredPlayers />,
    },
    {
      path: "dashboard/tournaments/edit/:title",
      element: <EditTournamentPage />,
    },
    { path: "dashboard/members", element: <ClubMembersPage /> },
    { path: "dashboard/payment-history", element: <ClubPaymentHistoryPage /> },
    {
      path: "dashboard/tournaments-earnings",
      element: <TournamentEarningsPage />,
    },
    { path: "dashboard/notifications", element: <ClubNotificationsPage /> },

    {
      path: "dashboard/notifications/club-join-requests",
      element: <ClubNotificationsPage />,
    },
    { path: "dashboard/coaching", element: <ComingSoon /> },
    { path: "dashboard/coaches", element: <ComingSoon /> },
    { path: "dashboard/pairing-system", element: <ComingSoon /> },
    { path: "dashboard/message", element: <ComingSoon /> },
  ],
  arbiter: [
    { path: "dashboard", element: <ArbiterDashboard /> },
    { path: "dashboard/tournaments", element: <ArbiterTournamentsPage /> },
    { path: "dashboard/tournaments/:title", element: <TournamentView /> },
    {
      path: "dashboard/tournaments/:title/attendance",
      element: <AttendanceMarkingPage />,
    },
    {
      path: "dashboard/tournaments/:title/leader-board",
      element: <AribiterLeaderBoard />,
    },
    {
      path: "dashboard/tournaments/:title/pairing-details",
      element: <ArbiterPairingDetails />,
    },
    {
      path: "dashboard/tournaments/:title/registeredplayers",
      element: <RegisteredPlayers />,
    },
    { path: "dashboard/profile", element: <ArbiterProfilePage /> },
    { path: "dashboard/profile/edit", element: <ArbiterProfileEdit /> },
    { path: "dashboard/notifications", element: <ArbiterNotificationPage /> },

    {
      path: "dashboard/notifications/tournament-jion-requests",
      element: <ArbiterNotificationPage />,
    },
    { path: "dashboard/my-chats", element: <ComingSoon /> },
  ],
  admin: [
    { path: "dashboard", element: <AdminDashboard /> },
    { path: "dashboard/content-management", element: <ContentPage /> },
    { path: "dashboard/players", element: <AdminPlayersPage /> },
    { path: "dashboard/clubs", element: <AdminClubsPage /> },
    { path: "dashboard/arbiters", element: <AdminArbitersPage /> },
    { path: "dashboard/players/:id", element: <PlayerDetails /> },
    ,
    { path: "dashboard/clubs/:id", element: <ClubsDetails /> },
    ,
    { path: "dashboard/arbiters/:id", element: <ArbiterDetails /> },
    { path: "dashboard/add", element: <AdminAdUploadPage /> },
    { path: "dashboard/tournaments", element: <AdminTournamentsPage /> },
    {
      path: "dashboard/tournaments/:title",
      element: <AdminTournamentDetailsPage />,
    },
    { path: "dashboard/payments", element: <AdminPaymentPage /> },
    {
      path: "dashboard/payments/details/:title",
      element: <PaymentTournamentDetails />,
    },
    { path: "dashboard/email", element: <MessagingPage /> },
    { path: "dashboard/sms", element: <MessagingPage /> },
    { path: "dashboard/whatsapp", element: <MessagingPage /> },
    { path: "dashboard/email/compose", element: <EmailComposer /> },
    { path: "dashboard/sms/compose", element: <SMSComposer /> },
    { path: "dashboard/whatsapp/compose", element: <WhatsAppComposer /> },
  ],
};

// Simple Protected Route Component
const ProtectedRoute = ({ children, allowedRoles, user }) => {
  const { isLoggedIn, authLoading } = useGlobalContext();

  // If auth is loading, show a spinner instead of redirecting
  // This prevents flashing to 404 page during auth refresh
  if (authLoading) {
    return (
      <Spinner
        fullScreen
        backgroundColor="#FFFFFF"
        message="Verifying your access..."
      />
    );
  }

  // Redirect to home if not logged in (only when not loading)
  if (!isLoggedIn) {
    return <Navigate to="/" replace />;
  }

  // Check role permissions (only when not loading)
  if (allowedRoles && !allowedRoles.includes(user?.role)) {
    // Redirect to the appropriate dashboard based on role
    return <Navigate to={`/dashboard`} replace />;
  }

  // All checks passed, render the protected content
  return children;
};

const AppRoutes = () => {
  const { user, isLoggedIn, authLoading } = useGlobalContext();
  // Helper function to render role-specific routes
  const renderRoleRoutes = () => {
    // If we have a user with a role, render their specific routes
    if (user?.role) {
      const roleRoutes = ROUTE_CONFIG[user?.role];
      return roleRoutes?.map((route) => (
        <Route
          key={route.path}
          path={route.path}
          element={
            <ProtectedRoute user={user} allowedRoles={[user?.role]}>
              {route.element}
            </ProtectedRoute>
          }
        />
      ));
    }

    // If auth is loading, don't return null - we'll handle this with a specific route below
    return null;
  };

  return (
    <Suspense fallback={<Spinner fullScreen backgroundColor="#FFFFFF" />}>
      <Routes>
        {/* Special case: Show spinner without header/footer during auth loading for dashboard routes */}
        {authLoading && (
          <Route
            path="/dashboard/*"
            element={
              <Spinner
                fullScreen
                backgroundColor="#FFFFFF"
                message="Loading your dashboard..."
              />
            }
          />
        )}

        {/* Main layout with header and footer for all other routes */}
        <Route element={<MainLayout />}>
          {/* Public Routes */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/clubs" element={<ClubsPage />} />
          <Route path="/clubs/:id" element={<ClubsDetailsPage />} />
          <Route path="/players" element={<PlayersPage />} />
          <Route path="/players/:id" element={<PlayerDetailsPage />} />
          <Route path="/tournaments" element={<TournamentsPage />} />
          <Route path="/coaches" element={<ComingSoon />} />
          <Route path="/associations" element={<ComingSoon />} />
          <Route
            path="/tournaments/:title"
            element={<TournamentDetailsPage />}
          />
          <Route
            path="/tournaments/:title/leader-board"
            element={<LeaderBoard />}
          />
          <Route
            path="/tournaments/:title/pairing-details"
            element={<PairingDetails />}
          />
          <Route
            path="/tournaments/:title/registered-players"
            element={<RegisteredPlayers />}
          />

          <Route path="/arbiters" element={<ArbitersPage />} />
          <Route path="/arbiters/:id" element={<ArbiterDetailsPage />} />
          {/* <Route path="/tournaments/:id/bulk-registration" element={<BulkRegistrationPage />} /> */}
          <Route path="/payment-failure" element={<PaymentFailurePage />} />
          <Route path="/payment-success" element={<PaymentSuccessPage />} />
          <Route path="/register" element={<SelectedPlayers />} />
          {/* <Route path="/pairing-details" element={<PairingDetails />} /> */}

          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/policy/:slug" element={<Policy />} />
          <Route path="/about-us" element={<AboutUs />} />

          {/* Protected Routes */}
          {renderRoleRoutes()}

          {/* Redirect unauthorized dashboard access when auth is not loading */}
          {!authLoading && !isLoggedIn && (
            <Route path="/dashboard/*" element={<Navigate to="/" replace />} />
          )}

          {/* 404 Not Found - Only show when not loading auth */}
          <Route
            path="*"
            element={
              !authLoading ? (
                <NotFound />
              ) : (
                <Spinner fullScreen backgroundColor="#FFFFFF" />
              )
            }
          />
        </Route>
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
