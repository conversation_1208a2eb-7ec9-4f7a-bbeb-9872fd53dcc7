Stack trace:
Frame         Function      Args
0007FFFFA0F0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8FF0) msys-2.0.dll+0x1FE8E
0007FFFFA0F0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3C8) msys-2.0.dll+0x67F9
0007FFFFA0F0  000210046832 (000210286019, 0007FFFF9FA8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0F0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA0F0  000210068E24 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3D0  00021006A225 (0007FFFFA100, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF817820000 ntdll.dll
7FF816620000 KERNEL32.DLL
7FF814A70000 KERNELBASE.dll
7FF815690000 USER32.dll
7FF8149B0000 win32u.dll
7FF817240000 GDI32.dll
7FF8151C0000 gdi32full.dll
7FF815110000 msvcp_win.dll
7FF814E40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF816B80000 advapi32.dll
7FF817000000 msvcrt.dll
7FF815A40000 sechost.dll
7FF817270000 RPCRT4.dll
7FF8140C0000 CRYPTBASE.DLL
7FF815300000 bcryptPrimitives.dll
7FF815950000 IMM32.DLL
